<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂平台') }}{% endblock %}</title>

    {% block meta %}{% endblock %}

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}

    <!-- Bootstrap 5.3.6 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN" crossorigin="anonymous">

    <!-- Font Awesome 6.5.1 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous">

    <!-- 插件CSS - Bootstrap 5兼容版本 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/2.1.4/toastr.min.css"
          integrity="sha512-6S2HWzVFxruDlZxI3sXOZZ4/eJ8AcxkQH1+JjSe/ONCEqR9L4Ysq5JdT5ipqtzU7WHalNwzwBv+iE51gNHJNqQ==" crossorigin="anonymous">

    <!-- 本地样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/local-fonts.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap5-theme.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme-colors.css') }}?v=2.4.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components-bs5.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-responsive.css') }}?v=1.0.0">

    <!-- Landing页面专用样式 -->
    <style>
        :root {
            --hero-gradient: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-rgb, 13, 110, 253) 100%);
            --card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            --card-hover-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        body.landing-page {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 导航栏样式 */
        .landing-navbar {
            background: var(--hero-gradient) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
        }

        .navbar-brand-container {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .navbar-brand-text {
            font-weight: 700;
            font-size: 1.5rem;
            color: white !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-brand-subtitle {
            font-size: 0.7rem;
            font-weight: 400;
            opacity: 0.8;
            margin-top: -2px;
            color: rgba(255,255,255,0.8) !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.3s ease;
            position: relative;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-2px);
        }

        /* 下拉菜单样式 */
        .dropdown-menu {
            background: white;
            border: none;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 10px;
        }

        .dropdown-item {
            padding: 12px 20px;
            color: #333;
            transition: all 0.3s ease;
        }

        .dropdown-item:hover {
            background: var(--hero-gradient);
            color: white;
            transform: translateX(5px);
        }

        /* 主要内容区域 */
        .landing-main {
            margin-top: 76px; /* 导航栏高度 */
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar-brand-text {
                font-size: 1.2rem;
            }

            .navbar-brand-subtitle {
                font-size: 0.6rem;
            }
        }
    </style>

    {% block styles %}{% endblock %}
</head>
<body data-theme="{{ theme_color|default('primary') }}" class="landing-page">

{% block content %}
<div class="landing-wrapper">
    <!-- 头部区域 -->
    <header class="landing-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1>{{ project_name|default('校园餐智慧食堂平台') }}</h1>
                    <p class="lead">为校园餐饮管理提供智能化解决方案</p>
                </div>
                <div class="col-lg-6">
                    {% if system_logo %}
                    <img src="{{ system_logo }}" alt="{{ project_name }}" class="img-fluid">
                    {% endif %}
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="landing-content">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <h3 class="feature-title">智能点餐</h3>
                        <p class="feature-description">
                            提供便捷的在线点餐服务，支持多种支付方式，让用餐更加便捷。
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">数据分析</h3>
                        <p class="feature-description">
                            实时监控餐饮数据，提供智能分析报告，助力决策优化。
                        </p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="feature-title">系统管理</h3>
                        <p class="feature-description">
                            完善的系统管理功能，支持多角色权限控制，确保系统安全。
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 行动召唤区域 -->
    <section class="cta-section">
        <div class="container">
            <h2 class="cta-title">立即开始使用</h2>
            <p class="cta-description">
                加入我们，体验智能化校园餐饮管理带来的便捷
            </p>
            {% if current_user.is_authenticated %}
            <a href="{{ url_for('main.dashboard') }}" class="btn btn-primary btn-lg cta-button">
                进入系统
            </a>
            {% else %}
            <a href="{{ url_for('auth.login') }}" class="btn btn-primary btn-lg cta-button">
                立即登录
            </a>
            {% endif %}
        </div>
    </section>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 添加滚动动画
    document.addEventListener('DOMContentLoaded', function() {
        const featureCards = document.querySelectorAll('.feature-card');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, {
            threshold: 0.1
        });

        featureCards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            observer.observe(card);
        });
    });
</script>
{% endblock %}
