{"python.defaultInterpreterPath": "${workspaceFolder}/venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "terminal.integrated.defaultProfile.windows": "PowerShell", "terminal.integrated.profiles.windows": {"PowerShell": {"path": "pwsh.exe", "args": ["-NoExit", "-Command", "& '${workspaceFolder}/venv/Scripts/Activate.ps1'"]}, "Command Prompt": {"path": "cmd.exe", "args": ["/k", "${workspaceFolder}/venv/Scripts/activate.bat"]}}, "terminal.integrated.env.windows": {"VIRTUAL_ENV": "${workspaceFolder}/venv"}, "terminal.integrated.inheritEnv": true, "python.terminal.activateEnvInCurrentTerminal": true, "python.terminal.executeInFileDir": true, "terminal.integrated.shellIntegration.enabled": true, "terminal.integrated.shellIntegration.decorationsEnabled": "both"}