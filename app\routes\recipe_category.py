from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from flask_login import login_required, current_user
from app import db
from app.models import RecipeCategory, AuditLog
import json

recipe_category_bp = Blueprint('recipe_category', __name__)

@recipe_category_bp.route('/')
@login_required
def index():
    """食谱分类列表页面"""
    categories = RecipeCategory.query.all()
    return render_template('recipe/categories.html', categories=categories)

@recipe_category_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建食谱分类"""
    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        category = RecipeCategory(
            name=name,
            description=description
        )

        db.session.add(category)

        # 添加审计日志 - 使用原始SQL避免日期时间精度问题
        from app.utils.log_activity import log_activity
        db.session.commit()  # 先提交以获取category.id
        log_activity(
            action='create',
            resource_type='RecipeCategory',
            resource_id=category.id,
            details=category.to_dict()
        )

        db.session.commit()
        flash('食谱分类创建成功！', 'success')
        return redirect(url_for('recipe_category.index'))

    # GET 请求，显示创建表单
    return render_template('recipe/category_form.html', category=None)

@recipe_category_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑食谱分类"""
    category = RecipeCategory.query.get_or_404(id)

    if request.method == 'POST':
        name = request.form.get('name')
        description = request.form.get('description')

        old_data = category.to_dict()

        category.name = name
        category.description = description

        # 添加审计日志 - 使用原始SQL避免日期时间精度问题
        from app.utils.log_activity import log_activity
        db.session.commit()  # 先提交以获取更新后的数据
        log_activity(
            action='update',
            resource_type='RecipeCategory',
            resource_id=category.id,
            details={
                'old': old_data,
                'new': category.to_dict()
            }
        )

        db.session.commit()
        flash('食谱分类更新成功！', 'success')
        return redirect(url_for('recipe_category.index'))

    # GET 请求，显示编辑表单
    return render_template('recipe/category_form.html', category=category)

@recipe_category_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除食谱分类"""
    category = RecipeCategory.query.get_or_404(id)

    # 检查是否有关联的食谱
    if category.recipes.count() > 0:
        return jsonify({'success': 0, 'message': '该分类下有关联的食谱，不能删除！'})

    # 添加审计日志 - 使用原始SQL避免日期时间精度问题
    from app.utils.log_activity import log_activity
    category_data = category.to_dict()  # 先获取数据

    log_activity(
        action='delete',
        resource_type='RecipeCategory',
        resource_id=category.id,
        details=category_data
    )

    db.session.delete(category)
    db.session.commit()

    return jsonify({'success': 1, 'message': '食谱分类删除成功！'})

@recipe_category_bp.route('/api')
@login_required
def api_list():
    """食谱分类API"""
    categories = RecipeCategory.query.all()
    return jsonify([category.to_dict() for category in categories])
