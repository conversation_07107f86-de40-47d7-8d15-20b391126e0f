[2025-05-08 12:03:17] 127.0.0.1 - api_fix_recipes_table - 成功 - 表结构已是最新
[2025-05-08 12:04:07] 127.0.0.1 - api_verify_database_structure - 成功 - 检查了 23 个表
[2025-05-08 12:05:58] 127.0.0.1 - api_fix_recipes_table - 成功 - 表结构已是最新
[2025-05-08 12:09:18] 127.0.0.1 - api_fix_food_samples_table - 成功 - 已添加列: operator_id (INT), destruction_time (DATETIME2), destruction_operator_id (INT)
[2025-05-08 12:12:01] 127.0.0.1 - api_fix_purchase_orders_table - 成功 - 创建了表: purchase_orders, purchase_order_items
