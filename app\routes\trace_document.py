from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import AuditLog
from app.models_ingredient_traceability import MaterialBatch, TraceDocument
from app import db
from datetime import datetime
import json
import uuid
import os
from werkzeug.utils import secure_filename

trace_document_bp = Blueprint('trace_document', __name__)

@trace_document_bp.route('/trace-document/upload/<int:batch_id>', methods=['GET', 'POST'])
@login_required
def upload(batch_id):
    """上传溯源文档"""
    batch = MaterialBatch.query.get_or_404(batch_id)
    
    # 检查用户是否有权限操作该批次
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限操作该批次', 'danger')
        return redirect(url_for('material_batch.view', id=batch_id))
    
    if request.method == 'POST':
        # 获取表单数据
        document_type = request.form.get('document_type')
        document_no = request.form.get('document_no')
        remark = request.form.get('remark')
        
        # 获取上传的文件
        if 'document_file' not in request.files:
            flash('没有选择文件', 'danger')
            return redirect(request.url)
        
        file = request.files['document_file']
        if file.filename == '':
            flash('没有选择文件', 'danger')
            return redirect(request.url)
        
        if file:
            # 确保文件名安全
            filename = secure_filename(file.filename)
            # 生成唯一文件名
            unique_filename = f"{uuid.uuid4().hex}_{filename}"
            # 确保上传目录存在
            upload_dir = os.path.join(current_app.static_folder, 'uploads/trace_documents')
            os.makedirs(upload_dir, exist_ok=1)
            # 保存文件
            file_path = os.path.join(upload_dir, unique_filename)
            file.save(file_path)
            
            # 创建文档记录
            document = TraceDocument(
                batch_id=batch_id,
                document_type=document_type,
                document_no=document_no,
                document_path=f"uploads/trace_documents/{unique_filename}",
                uploader_id=current_user.id,
                remark=remark
            )
            
            db.session.add(document)
            
            # 添加审计日志 - 使用原始SQL避免日期时间精度问题
            from app.utils.log_activity import log_activity
            log_activity(
                action='create',
                resource_type='TraceDocument',
                resource_id=document.id,
                details=document.to_dict()
            )
            
            db.session.commit()
            flash('文档上传成功！', 'success')
            return redirect(url_for('material_batch.view', id=batch_id))
    
    return render_template('trace_document/upload.html', batch=batch)

@trace_document_bp.route('/trace-document/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除溯源文档"""
    document = TraceDocument.query.get_or_404(id)
    batch = MaterialBatch.query.get(document.batch_id)
    
    # 检查用户是否有权限操作该批次
    if not current_user.can_access_area_by_id(batch.area_id):
        return jsonify({'success': 0, 'message': '您没有权限操作该批次'})
    
    # 添加审计日志 - 使用原始SQL避免日期时间精度问题
    from app.utils.log_activity import log_activity
    document_data = document.to_dict()  # 先获取数据

    log_activity(
        action='delete',
        resource_type='TraceDocument',
        resource_id=document.id,
        details=document_data
    )
    
    # 删除文件
    if document.document_path:
        file_path = os.path.join(current_app.static_folder, document.document_path)
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # 删除记录
    db.session.delete(document)
    db.session.commit()
    
    return jsonify({'success': 1, 'message': '文档删除成功'})

@trace_document_bp.route('/trace-document/<int:id>')
@login_required
def view(id):
    """查看溯源文档"""
    document = TraceDocument.query.get_or_404(id)
    batch = MaterialBatch.query.get(document.batch_id)
    
    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限查看该文档', 'danger')
        return redirect(url_for('material_batch.index'))
    
    return render_template('trace_document/view.html', document=document, batch=batch)
