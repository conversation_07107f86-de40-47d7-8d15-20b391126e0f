[2025-05-08 20:14:45] admin(1) - fix_food_samples_table - 成功 - 表结构已是最新
[2025-05-08 20:14:48] admin(1) - fix_recipes_table - 成功 - 表结构已是最新
[2025-05-08 20:14:50] admin(1) - fix_purchase_orders_table - 成功 - 表已存在
[2025-05-08 20:14:51] admin(1) - verify_database_structure - 成功 - 检查了 87 个表
[2025-05-08 20:14:52] admin(1) - fix_missing_data - 成功 - 行政区域表中有 38 条数据
[2025-05-08 20:14:53] admin(1) - fix_missing_data - 成功 - 行政区域表中有 38 条数据
[2025-05-08 20:14:55] admin(1) - enhanced_auto_fix - 失败 - Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\routes.py", line 352, in enhanced_auto_fix
    result = enhanced_auto_fix_all()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\enhanced_auto_fix.py", line 386, in enhanced_auto_fix_all
    fixed_tables = check_and_fix_missing_tables()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\enhanced_auto_fix.py", line 167, in check_and_fix_missing_tables
    app = create_app()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py", line 31, in create_app
    admin.init_app(app)
AttributeError: module 'app.admin' has no attribute 'init_app'

[2025-05-08 20:14:58] admin(1) - enhanced_auto_fix - 失败 - Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\routes.py", line 352, in enhanced_auto_fix
    result = enhanced_auto_fix_all()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\enhanced_auto_fix.py", line 386, in enhanced_auto_fix_all
    fixed_tables = check_and_fix_missing_tables()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\system_fix\enhanced_auto_fix.py", line 167, in check_and_fix_missing_tables
    app = create_app()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py", line 31, in create_app
    admin.init_app(app)
AttributeError: module 'app.admin' has no attribute 'init_app'

[2025-05-08 20:14:58] admin(1) - fix_recipes_table - 成功 - 表结构已是最新
[2025-05-08 20:14:58] admin(1) - fix_food_samples_table - 成功 - 表结构已是最新
[2025-05-08 20:14:58] admin(1) - fix_purchase_orders_table - 成功 - 表已存在
[2025-05-08 20:14:58] admin(1) - verify_database_structure - 成功 - 检查了 87 个表
[2025-05-08 20:14:58] admin(1) - fix_missing_data - 成功 - 行政区域表中有 38 条数据
[2025-05-08 20:14:58] admin(1) - fix_all - 成功 - 执行了所有修复操作
