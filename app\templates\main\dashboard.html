{% extends 'base_sidebar.html' %}

{% block title %}控制面板 - {{ super() }}{% endblock %}

{% block styles %}
<style>
    /* 控制台页面专用样式 - Bootstrap 5.3.6 */
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .card-footer {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        padding: 0.75rem 1.5rem;
    }

    .dashboard-card .card-footer a {
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .dashboard-card .card-footer a:hover {
        transform: translateX(5px);
    }

    .stats-icon {
        opacity: 0.8;
        transition: all 0.3s ease;
    }

    .dashboard-card:hover .stats-icon {
        opacity: 1;
        transform: scale(1.1);
    }

    .recent-orders-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .quick-actions-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }

    .list-group-item {
        border: none;
        border-radius: 10px !important;
        margin-bottom: 8px;
        transition: all 0.3s ease;
        background: var(--bs-light);
    }

    .list-group-item:hover {
        background: var(--bs-primary);
        color: white;
        transform: translateX(10px);
    }

    .list-group-item:hover i {
        color: white;
    }

    .mobile-order-card {
        border: none;
        border-radius: 12px;
        border-left: 4px solid var(--bs-primary) !important;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .mobile-order-card:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .action-buttons .btn {
        margin: 5px;
        border-radius: 25px;
        padding: 8px 16px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        .dashboard-card .card-body {
            padding: 1rem;
        }

        .stats-icon.mobile-hidden {
            display: none !important;
        }

        .stats-icon.mobile-only {
            display: block !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h2>控制面板</h2>
        <p class="text-muted">欢迎回来，{{ current_user.real_name or current_user.username }}</p>
    </div>
</div>

<div class="row">
    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card text-white bg-primary dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2">供应商</h6>
                        <h2 class="mb-0 fw-bold">{{ suppliers_count }}</h2>
                    </div>
                    <i class="fas fa-building fa-3x stats-icon mobile-hidden"></i>
                    <i class="fas fa-building fa-2x stats-icon mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.suppliers') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card text-white bg-success dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2">食材</h6>
                        <h2 class="mb-0 fw-bold">{{ ingredients_count }}</h2>
                    </div>
                    <i class="fas fa-carrot fa-3x stats-icon mobile-hidden"></i>
                    <i class="fas fa-carrot fa-2x stats-icon mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.ingredients') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card text-white bg-warning dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2">食谱</h6>
                        <h2 class="mb-0 fw-bold">{{ recipes_count }}</h2>
                    </div>
                    <i class="fas fa-utensils fa-3x stats-icon mobile-hidden"></i>
                    <i class="fas fa-utensils fa-2x stats-icon mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.recipes') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-4">
        <div class="card text-white bg-danger dashboard-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-2">留样</h6>
                        <h2 class="mb-0 fw-bold">{{ samples_count }}</h2>
                    </div>
                    <i class="fas fa-vial fa-3x stats-icon mobile-hidden"></i>
                    <i class="fas fa-vial fa-2x stats-icon mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="{{ url_for('main.food_samples') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 col-12">
        <div class="card recent-orders-card mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="mb-0 fw-bold">最近采购订单</h5>
            </div>
            <div class="card-body">
                <!-- 桌面端表格 -->
                <div class="table-responsive d-none d-md-block">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>供应商</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>{{ order.id }}</td>
                                <td>{{ order.supplier.name }}</td>
                                <td>¥{{ order.total_amount }}</td>
                                <td>
                                    {% if order.status == '待审核' %}
                                    <span class="badge bg-warning text-dark">{{ order.status }}</span>
                                    {% elif order.status == '已发货' %}
                                    <span class="badge bg-info">{{ order.status }}</span>
                                    {% elif order.status == '已完成' %}
                                    <span class="badge bg-success">{{ order.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{  order.order_date|format_datetime('%Y-%m-%d')  }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无采购订单</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- 移动端卡片 -->
                <div class="d-md-none">
                    {% for order in recent_orders %}
                    <div class="card mb-2 mobile-order-card">
                        <div class="card-body py-3">
                            <div class="row">
                                <div class="col-8">
                                    <h6 class="mb-1">订单 #{{ order.id }}</h6>
                                    <small class="text-muted">{{ order.supplier.name }}</small>
                                </div>
                                <div class="col-4 text-end">
                                    <div class="fw-bold">¥{{ order.total_amount }}</div>
                                    {% if order.status == '待审核' %}
                                    <span class="badge bg-warning text-dark">{{ order.status }}</span>
                                    {% elif order.status == '已发货' %}
                                    <span class="badge bg-info">{{ order.status }}</span>
                                    {% elif order.status == '已完成' %}
                                    <span class="badge bg-success">{{ order.status }}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ order.status }}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-12">
                                    <small class="text-muted">{{ order.order_date|format_datetime('%Y-%m-%d') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无采购订单</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="{{ url_for('main.purchase_orders') }}" class="btn btn-sm btn-primary">查看所有订单</a>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-12">
        <div class="card quick-actions-card mb-4">
            <div class="card-header bg-transparent border-0 pb-0">
                <h5 class="mb-0 fw-bold">快捷操作</h5>
            </div>
            <div class="card-body">
                <!-- 桌面端列表 -->
                <div class="list-group d-none d-md-block">
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加供应商
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加食材
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加食谱
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 创建采购订单
                    </a>
                    <a href="#" class="list-group-item list-group-item-action">
                        <i class="fas fa-plus-circle me-2"></i> 添加留样记录
                    </a>
                </div>

                <!-- 移动端按钮 -->
                <div class="action-buttons d-md-none">
                    <a href="#" class="btn btn-outline-primary">
                        <i class="fas fa-plus-circle"></i> 添加供应商
                    </a>
                    <a href="#" class="btn btn-outline-success">
                        <i class="fas fa-plus-circle"></i> 添加食材
                    </a>
                    <a href="#" class="btn btn-outline-warning">
                        <i class="fas fa-plus-circle"></i> 添加食谱
                    </a>
                    <a href="#" class="btn btn-outline-info">
                        <i class="fas fa-plus-circle"></i> 创建采购订单
                    </a>
                    <a href="#" class="btn btn-outline-danger">
                        <i class="fas fa-plus-circle"></i> 添加留样记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
