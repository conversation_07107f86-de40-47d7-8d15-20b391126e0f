from flask import redirect, url_for, flash, jsonify
from flask_login import login_required
from flask import Blueprint

inspection_bp = Blueprint('inspection', __name__)

# 入库检查首页
@inspection_bp.route('/')
@login_required
def index():
    """入库检查首页 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))

# 禁用其他路由
@inspection_bp.route('/quick-pass', methods=['POST'])
@login_required
def quick_pass():
    """快速通过检查 - 功能暂时不可用"""
    return jsonify({'success': False, 'message': '入库检查功能暂时不可用'})

@inspection_bp.route('/original')
@login_required
def original_index():
    """原始入库检查首页 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))

@inspection_bp.route('/create/<int:purchase_order_id>')
@login_required
def create(purchase_order_id):
    """创建入库检查记录 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))

@inspection_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑入库检查记录 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))

@inspection_bp.route('/view/<int:id>')
@login_required
def view(id):
    """查看入库检查记录 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))

@inspection_bp.route('/save-inspection', methods=['POST'])
@login_required
def save_inspection():
    """保存简化版入库检查结果 - 功能暂时不可用"""
    flash('入库检查功能暂时不可用，该功能在当前阶段没有实际意义', 'info')
    return redirect(url_for('main.index'))
