"""
产品批量上架模块的路由
"""
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, session
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, Ingredient, IngredientCategory, SupplierProduct, SupplierSchoolRelation
from app.models_product_batch import StandardUnit, CategoryUnitMapping, ProductBatch
from app.forms.product_batch import (
    ProductBatchForm, ProductBatchIngredientForm, ProductBatchAttributeForm,
    ProductBatchAdjustForm, ProductBatchConfirmForm, ProductBatchApproveForm
)
from app.utils.log_activity import log_activity
import json
from datetime import datetime

product_batch_bp = Blueprint('product_batch', __name__)

@product_batch_bp.route('/')
@login_required
def index():
    """产品批量上架首页"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    status = request.args.get('status', '')

    # 查询批次列表
    query = ProductBatch.query

    if status:
        query = query.filter_by(status=status)

    # 非管理员只能看到自己创建的批次
    if not current_user.is_admin():
        query = query.filter_by(created_by=current_user.id)

    pagination = query.order_by(ProductBatch.created_at.desc()).paginate(page=page, per_page=per_page)
    batches = pagination.items

    # 确保日期时间字段已正确处理
    for batch in batches:
        if hasattr(batch, 'created_at') and batch.created_at and hasattr(batch.created_at, 'strftime'):
            batch.created_at = batch.created_at.strftime('%Y-%m-%d %H:%M:%S')
        if hasattr(batch, 'updated_at') and batch.updated_at and hasattr(batch.updated_at, 'strftime'):
            batch.updated_at = batch.updated_at.strftime('%Y-%m-%d %H:%M:%S')

    # 获取分类列表
    categories = IngredientCategory.query.all()

    return render_template('product_batch/index.html',
                          batches=batches,
                          pagination=pagination,
                          categories=categories,
                          status=status,
                          title='产品批量上架管理')

@product_batch_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建产品批次"""
    form = ProductBatchForm()

    # 获取分类选项
    categories = IngredientCategory.query.all()
    form.category_id.choices = [(0, '-- 请选择分类 --')] + [(c.id, c.name) for c in categories]

    # 获取供应商选项，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .filter(SupplierSchoolRelation.status == 1)\
                    .distinct().all()

    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    if form.validate_on_submit():
        # 生成批次号作为名称
        from app.utils.batch_number_generator import generate_batch_number

        batch_number = generate_batch_number()

        # 创建批次
        batch = ProductBatch(
            name=batch_number,  # 直接使用批次号作为名称
            category_id=form.category_id.data,
            supplier_id=form.supplier_id.data,
            created_by=current_user.id,
            status='pending'
        )
        db.session.add(batch)
        db.session.commit()

        # 记录审计日志
        log_activity(
            action='create',
            resource_type='ProductBatch',
            resource_id=batch.id,
            details={
                'batch_number': batch.name,  # 批次号存储在name字段中
                'category_id': batch.category_id,
                'supplier_id': batch.supplier_id
            }
        )

        flash('批次创建成功，请选择食材', 'success')
        return redirect(url_for('product_batch.select_ingredients', id=batch.id))

    return render_template('product_batch/create.html', form=form, title='创建产品批次')

@product_batch_bp.route('/<int:id>/select_ingredients', methods=['GET', 'POST'])
@login_required
def select_ingredients(id):
    """选择食材"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        flash('您没有权限操作此批次', 'danger')
        return redirect(url_for('product_batch.index'))

    form = ProductBatchIngredientForm()
    form.batch_id.data = id

    # 获取该分类下的所有食材 - 实现学校级数据隔离
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    ingredients = Ingredient.query.filter(
        Ingredient.category_id == batch.category_id,
        Ingredient.status == 1,
        db.or_(
            Ingredient.area_id.in_(area_ids),  # 用户学校的食材
            Ingredient.is_global == True,      # 全局食材（系统预设）
            Ingredient.area_id.is_(None)       # 兼容旧数据（无area_id的食材）
        )
    ).all()
    form.ingredients.choices = [(i.id, i.name) for i in ingredients]

    if form.validate_on_submit():
        # 保存选中的食材ID到会话
        session['selected_ingredients'] = form.ingredients.data

        flash('食材选择成功，请设置通用属性', 'success')
        return redirect(url_for('product_batch.set_attributes', id=batch.id))

    return render_template('product_batch/select_ingredients.html',
                          form=form,
                          batch=batch,
                          ingredients=ingredients,
                          title='选择食材')

@product_batch_bp.route('/<int:id>/set_attributes', methods=['GET', 'POST'])
@login_required
def set_attributes(id):
    """设置通用属性"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        flash('您没有权限操作此批次', 'danger')
        return redirect(url_for('product_batch.index'))

    # 检查是否已选择食材
    if 'selected_ingredients' not in session:
        flash('请先选择食材', 'danger')
        return redirect(url_for('product_batch.select_ingredients', id=batch.id))

    form = ProductBatchAttributeForm()
    form.batch_id.data = id

    # 获取该分类的默认单位
    default_units = db.session.query(StandardUnit)\
        .join(CategoryUnitMapping, StandardUnit.id == CategoryUnitMapping.unit_id)\
        .filter(CategoryUnitMapping.category_id == batch.category_id)\
        .all()

    # 如果没有默认单位，则获取所有单位
    if not default_units:
        default_units = StandardUnit.query.all()

    form.default_unit_id.choices = [(u.id, f"{u.name} ({u.symbol})") for u in default_units]

    # 设置默认单位
    primary_unit = db.session.query(StandardUnit)\
        .join(CategoryUnitMapping, StandardUnit.id == CategoryUnitMapping.unit_id)\
        .filter(CategoryUnitMapping.category_id == batch.category_id, CategoryUnitMapping.is_primary == 1)\
        .first()

    if primary_unit:
        form.default_unit_id.default = primary_unit.id

    if form.validate_on_submit():
        # 保存通用属性到会话
        session['batch_attributes'] = {
            'price_strategy': form.price_strategy.data,
            'fixed_price': form.fixed_price.data,
            'quality_cert': form.quality_cert.data,
            'quality_standard': form.quality_standard.data,
            'lead_time': form.lead_time.data,
            'min_order_quantity': form.min_order_quantity.data,
            'default_unit_id': form.default_unit_id.data
        }

        flash('通用属性设置成功，请进行个性化调整', 'success')
        return redirect(url_for('product_batch.adjust_products', id=batch.id))

    return render_template('product_batch/set_attributes.html',
                          form=form,
                          batch=batch,
                          title='设置通用属性')

@product_batch_bp.route('/<int:id>/adjust_products', methods=['GET', 'POST'])
@login_required
def adjust_products(id):
    """个性化调整产品"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        flash('您没有权限操作此批次', 'danger')
        return redirect(url_for('product_batch.index'))

    # 检查是否已设置通用属性
    if 'batch_attributes' not in session:
        flash('请先设置通用属性', 'danger')
        return redirect(url_for('product_batch.set_attributes', id=batch.id))

    # 检查是否已选择食材
    if 'selected_ingredients' not in session:
        flash('请先选择食材', 'danger')
        return redirect(url_for('product_batch.select_ingredients', id=batch.id))

    form = ProductBatchAdjustForm()
    form.batch_id.data = id

    # 获取选中的食材
    ingredient_ids = session.get('selected_ingredients', [])
    ingredients = Ingredient.query.filter(Ingredient.id.in_(ingredient_ids)).all()

    # 获取所有单位
    units = StandardUnit.query.all()

    # 获取通用属性
    attributes = session.get('batch_attributes', {})
    default_unit_id = attributes.get('default_unit_id')
    default_unit = StandardUnit.query.get(default_unit_id)

    if form.validate_on_submit():
        # 获取表单中的产品数据
        product_data = json.loads(request.form.get('product_data', '[]'))

        # 保存产品数据到会话
        session['product_data'] = product_data

        flash('个性化调整成功，请确认产品信息', 'success')
        return redirect(url_for('product_batch.confirm', id=batch.id))

    return render_template('product_batch/adjust_products.html',
                          form=form,
                          batch=batch,
                          ingredients=ingredients,
                          units=units,
                          default_unit=default_unit,
                          attributes=attributes,
                          title='个性化调整产品')

@product_batch_bp.route('/<int:id>/confirm', methods=['GET', 'POST'])
@login_required
def confirm(id):
    """确认产品信息"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        flash('您没有权限操作此批次', 'danger')
        return redirect(url_for('product_batch.index'))

    # 检查是否已进行个性化调整
    if 'product_data' not in session:
        flash('请先进行个性化调整', 'danger')
        return redirect(url_for('product_batch.adjust_products', id=batch.id))

    form = ProductBatchConfirmForm()
    form.batch_id.data = id

    # 获取产品数据
    product_data = session.get('product_data', [])

    # 获取单位信息
    unit_ids = [p.get('unit_id') for p in product_data]
    units = {u.id: u for u in StandardUnit.query.filter(StandardUnit.id.in_(unit_ids)).all()}

    # 获取食材信息
    ingredient_ids = [p.get('ingredient_id') for p in product_data]
    ingredients = {i.id: i for i in Ingredient.query.filter(Ingredient.id.in_(ingredient_ids)).all()}

    if form.validate_on_submit():
        try:
            # 使用系统自带的数据库操作功能，逐个添加产品
            from sqlalchemy import text

            for data in product_data:
                ingredient_id = data.get('ingredient_id')
                ingredient = ingredients.get(ingredient_id)
                unit = units.get(data.get('unit_id'))

                # 使用原生SQL语句插入数据，确保日期时间精度正确
                sql = text("""
                INSERT INTO supplier_products
                (supplier_id, ingredient_id, product_code, product_name, model_number, specification,
                price, quality_cert, quality_standard, lead_time, min_order_quantity,
                is_available, shelf_status, description, batch_id, created_at, updated_at)
                VALUES
                (:supplier_id, :ingredient_id, :product_code, :product_name, :model_number, :specification,
                :price, :quality_cert, :quality_standard, :lead_time, :min_order_quantity,
                :is_available, :shelf_status, :description, :batch_id, GETDATE(), GETDATE())
                """)

                # 准备参数
                params = {
                    'supplier_id': batch.supplier_id,
                    'ingredient_id': ingredient_id,
                    'product_code': data.get('product_code', ''),
                    'product_name': data.get('product_name', ingredient.name if ingredient else ''),
                    'model_number': data.get('model_number', ''),
                    'specification': f"{data.get('specification_value', '')} {unit.symbol if unit else ''}",
                    'price': data.get('price', 0),
                    'quality_cert': data.get('quality_cert', ''),
                    'quality_standard': data.get('quality_standard', ''),
                    'lead_time': data.get('lead_time', 0),
                    'min_order_quantity': data.get('min_order_quantity', 0),
                    'description': data.get('description', ''),
                    'is_available': 0,  # 默认未上架
                    'shelf_status': 0,  # 默认待审核
                    'batch_id': batch.id
                }

                # 执行SQL
                db.session.execute(sql, params)
                db.session.commit()

            # 更新批次状态
            batch.status = 'pending'

            # 使用原生SQL更新批次状态，确保日期时间精度正确
            update_sql = text("""
            UPDATE product_batches
            SET status = 'pending', updated_at = GETDATE()
            WHERE id = :batch_id
            """)

            db.session.execute(update_sql, {'batch_id': batch.id})
            db.session.commit()

            # 记录审计日志
            log_activity(
                action='create_batch_products',
                resource_type='ProductBatch',
                resource_id=batch.id,
                details={
                    'product_count': len(product_data),
                    'batch_name': batch.name
                }
            )

            # 清除会话数据
            session.pop('selected_ingredients', None)
            session.pop('batch_attributes', None)
            session.pop('product_data', None)

            flash(f'成功创建 {len(product_data)} 个产品，等待审核', 'success')
            return redirect(url_for('product_batch.index'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"批量创建产品失败: {str(e)}")
            flash(f'创建产品失败: {str(e)}', 'danger')

    return render_template('product_batch/confirm.html',
                          form=form,
                          batch=batch,
                          product_data=product_data,
                          units=units,
                          ingredients=ingredients,
                          title='确认产品信息')

@product_batch_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看批次详情"""
    batch = ProductBatch.query.get_or_404(id)

    # 确保日期时间字段已正确处理
    if hasattr(batch, 'created_at') and batch.created_at and hasattr(batch.created_at, 'strftime'):
        batch.created_at = batch.created_at.strftime('%Y-%m-%d %H:%M:%S')
    if hasattr(batch, 'updated_at') and batch.updated_at and hasattr(batch.updated_at, 'strftime'):
        batch.updated_at = batch.updated_at.strftime('%Y-%m-%d %H:%M:%S')

    # 获取批次下的产品
    products = SupplierProduct.query.filter_by(batch_id=batch.id).all()

    return render_template('product_batch/view.html',
                          batch=batch,
                          products=products,
                          title='批次详情')

@product_batch_bp.route('/<int:id>/approve', methods=['GET', 'POST'])
@login_required
def approve(id):
    """审核批次"""
    batch = ProductBatch.query.get_or_404(id)

    # 确保日期时间字段已正确处理
    if hasattr(batch, 'created_at') and batch.created_at and hasattr(batch.created_at, 'strftime'):
        batch.created_at = batch.created_at.strftime('%Y-%m-%d %H:%M:%S')
    if hasattr(batch, 'updated_at') and batch.updated_at and hasattr(batch.updated_at, 'strftime'):
        batch.updated_at = batch.updated_at.strftime('%Y-%m-%d %H:%M:%S')

    # 检查权限
    if not current_user.is_admin():
        flash('您没有权限审核批次', 'danger')
        return redirect(url_for('product_batch.index'))

    form = ProductBatchApproveForm()
    form.batch_id.data = id

    # 获取批次下的产品
    products = SupplierProduct.query.filter_by(batch_id=batch.id).all()

    if form.validate_on_submit():
        try:
            if form.approve_all.data:
                # 使用系统自带的数据库操作功能批量审核通过
                from sqlalchemy import text

                # 批量更新产品状态
                update_products_sql = text("""
                UPDATE supplier_products
                SET shelf_status = 1, updated_at = GETDATE()
                WHERE batch_id = :batch_id
                """)

                db.session.execute(update_products_sql, {'batch_id': batch.id})
                db.session.commit()

                # 更新批次状态
                update_batch_sql = text("""
                UPDATE product_batches
                SET status = 'approved', updated_at = GETDATE()
                WHERE id = :batch_id
                """)

                db.session.execute(update_batch_sql, {'batch_id': batch.id})

                # 记录审计日志
                log_activity(
                    action='approve_batch',
                    resource_type='ProductBatch',
                    resource_id=batch.id,
                    details={
                        'product_count': len(products),
                        'batch_name': batch.name
                    }
                )
            else:
                # 使用系统自带的数据库操作功能拒绝批次
                from sqlalchemy import text

                # 批量更新产品状态
                update_products_sql = text("""
                UPDATE supplier_products
                SET shelf_status = 2, updated_at = GETDATE()
                WHERE batch_id = :batch_id
                """)

                db.session.execute(update_products_sql, {'batch_id': batch.id})
                db.session.commit()

                # 更新批次状态
                update_batch_sql = text("""
                UPDATE product_batches
                SET status = 'rejected', updated_at = GETDATE()
                WHERE id = :batch_id
                """)

                db.session.execute(update_batch_sql, {'batch_id': batch.id})

                # 记录审计日志
                log_activity(
                    action='reject_batch',
                    resource_type='ProductBatch',
                    resource_id=batch.id,
                    details={
                        'product_count': len(products),
                        'batch_name': batch.name,
                        'reject_reason': form.reject_reason.data
                    }
                )

            db.session.commit()

            if form.approve_all.data:
                flash('批次审核通过成功', 'success')
            else:
                flash('批次已拒绝', 'success')

            return redirect(url_for('product_batch.index'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"审核批次失败: {str(e)}")
            flash(f'审核失败: {str(e)}', 'danger')

    return render_template('product_batch/approve.html',
                          form=form,
                          batch=batch,
                          products=products,
                          title='审核批次')

@product_batch_bp.route('/<int:id>/shelf', methods=['POST'])
@login_required
def shelf_batch(id):
    """上架批次"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        return jsonify({'success': 0, 'message': '您没有权限操作此批次'})

    # 检查批次状态
    if batch.status != 'approved':
        return jsonify({'success': 0, 'message': '只有已审核的批次才能上架'})

    try:
        # 获取批次下的产品
        products = SupplierProduct.query.filter_by(batch_id=batch.id).all()

        # 使用系统自带的数据库操作功能批量上架
        from sqlalchemy import text

        # 批量更新产品状态
        update_products_sql = text("""
        UPDATE supplier_products
        SET is_available = 1, shelf_time = GETDATE(), shelf_operator_id = :operator_id, updated_at = GETDATE()
        WHERE batch_id = :batch_id
        """)

        db.session.execute(update_products_sql, {'batch_id': batch.id, 'operator_id': current_user.id})
        db.session.commit()

        # 更新批次状态
        update_batch_sql = text("""
        UPDATE product_batches
        SET status = 'shelved', updated_at = GETDATE()
        WHERE id = :batch_id
        """)

        db.session.execute(update_batch_sql, {'batch_id': batch.id})

        db.session.commit()

        # 记录审计日志
        log_activity(
            action='shelf_batch',
            resource_type='ProductBatch',
            resource_id=batch.id,
            details={
                'product_count': len(products),
                'batch_name': batch.name
            }
        )

        return jsonify({'success': 1, 'message': '批次上架成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"上架批次失败: {str(e)}")
        return jsonify({'success': 0, 'message': f'上架失败: {str(e)}'})

@product_batch_bp.route('/<int:id>/unshelf', methods=['POST'])
@login_required
def unshelf_batch(id):
    """下架批次"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        return jsonify({'success': 0, 'message': '您没有权限操作此批次'})

    # 检查批次状态
    if batch.status != 'shelved':
        return jsonify({'success': 0, 'message': '只有已上架的批次才能下架'})

    try:
        # 使用系统自带的数据库操作功能批量下架
        from sqlalchemy import text

        # 批量更新产品状态
        update_products_sql = text("""
        UPDATE supplier_products
        SET is_available = 0, updated_at = GETDATE()
        WHERE batch_id = :batch_id
        """)

        db.session.execute(update_products_sql, {'batch_id': batch.id})
        db.session.commit()

        # 更新批次状态
        update_batch_sql = text("""
        UPDATE product_batches
        SET status = 'approved', updated_at = GETDATE()
        WHERE id = :batch_id
        """)

        db.session.execute(update_batch_sql, {'batch_id': batch.id})

        db.session.commit()

        # 记录审计日志
        log_activity(
            action='unshelf_batch',
            resource_type='ProductBatch',
            resource_id=batch.id,
            details={
                'batch_name': batch.name
            }
        )

        return jsonify({'success': 1, 'message': '批次下架成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"下架批次失败: {str(e)}")
        return jsonify({'success': 0, 'message': f'下架失败: {str(e)}'})

@product_batch_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete_batch(id):
    """删除批次"""
    batch = ProductBatch.query.get_or_404(id)

    # 检查权限
    if not current_user.is_admin() and batch.created_by != current_user.id:
        return jsonify({'success': 0, 'message': '您没有权限操作此批次'})

    # 检查批次状态
    if batch.status == 'shelved':
        return jsonify({'success': 0, 'message': '已上架的批次不能删除，请先下架'})

    try:
        # 使用系统自带的数据库操作功能删除批次及其产品
        from sqlalchemy import text

        # 保存批次信息用于审计日志
        batch_id = batch.id
        batch_name = batch.name

        # 先删除批次下的产品
        delete_products_sql = text("""
        DELETE FROM supplier_products
        WHERE batch_id = :batch_id
        """)

        db.session.execute(delete_products_sql, {'batch_id': batch_id})
        db.session.commit()

        # 再删除批次
        delete_batch_sql = text("""
        DELETE FROM product_batches
        WHERE id = :batch_id
        """)

        db.session.execute(delete_batch_sql, {'batch_id': batch_id})
        db.session.commit()

        # 记录审计日志 - 使用之前保存的信息，而不是已删除的对象
        log_activity(
            action='delete_batch',
            resource_type='ProductBatch',
            resource_id=batch_id,
            details={
                'batch_name': batch_name
            }
        )

        return jsonify({'success': 1, 'message': '批次删除成功'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除批次失败: {str(e)}")
        return jsonify({'success': 0, 'message': f'删除失败: {str(e)}'})

@product_batch_bp.route('/api/units')
@login_required
def api_units():
    """获取单位列表"""
    unit_type = request.args.get('type', '')

    query = StandardUnit.query

    if unit_type:
        query = query.filter_by(unit_type=unit_type)

    units = query.all()

    return jsonify([u.to_dict() for u in units])

@product_batch_bp.route('/api/category_units/<int:category_id>')
@login_required
def api_category_units(category_id):
    """获取分类的单位列表"""
    units = db.session.query(StandardUnit)\
        .join(CategoryUnitMapping, StandardUnit.id == CategoryUnitMapping.unit_id)\
        .filter(CategoryUnitMapping.category_id == category_id)\
        .all()

    return jsonify([u.to_dict() for u in units])
