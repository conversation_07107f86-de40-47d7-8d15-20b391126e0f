from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Recipe
from app.models_recipe_advanced import RecipeReview
from datetime import datetime

recipe_rating_bp = Blueprint('recipe_rating', __name__)

@recipe_rating_bp.route('/api/rate/<int:recipe_id>', methods=['POST'])
@login_required
def rate_recipe(recipe_id):
    """评价食谱"""
    recipe = Recipe.query.get_or_404(recipe_id)
    
    # 获取评分和评论
    rating = request.form.get('rating', type=int)
    comment = request.form.get('comment', '')
    
    # 验证评分
    if not rating or rating < 1 or rating > 5:
        return jsonify({
            'status': 'error',
            'message': '请提供有效的评分（1-5星）'
        })
    
    # 检查用户是否已评价过该食谱
    existing_review = RecipeReview.query.filter_by(
        recipe_id=recipe_id,
        user_id=current_user.id
    ).first()
    
    if existing_review:
        # 更新现有评价
        existing_review.rating = rating
        existing_review.comment = comment
        existing_review.updated_at = datetime.now().replace(microsecond=0)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'action': 'updated',
            'message': '评价已更新'
        })
    else:
        # 创建新评价
        # 获取用户所在区域ID
        area_id = current_user.area_id
        
        new_review = RecipeReview(
            recipe_id=recipe_id,
            user_id=current_user.id,
            area_id=area_id,
            rating=rating,
            comment=comment,
            usage_date=datetime.now().date(),
            is_public=True
        )
        
        db.session.add(new_review)
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'action': 'created',
            'message': '评价已提交'
        })

@recipe_rating_bp.route('/api/get-rating/<int:recipe_id>')
def get_recipe_rating(recipe_id):
    """获取食谱评分信息"""
    recipe = Recipe.query.get_or_404(recipe_id)
    
    # 获取食谱的所有评价
    reviews = RecipeReview.query.filter_by(recipe_id=recipe_id).all()
    
    # 计算平均评分
    if reviews:
        avg_rating = sum(review.rating for review in reviews) / len(reviews)
        review_count = len(reviews)
    else:
        avg_rating = 0
        review_count = 0
    
    # 获取当前用户的评价（如果已登录）
    user_review = None
    if current_user.is_authenticated:
        user_review = RecipeReview.query.filter_by(
            recipe_id=recipe_id,
            user_id=current_user.id
        ).first()
    
    # 构建响应数据
    response = {
        'recipe_id': recipe_id,
        'recipe_name': recipe.name,
        'avg_rating': round(avg_rating, 1),
        'review_count': review_count,
        'user_has_reviewed': user_review is not None
    }
    
    # 如果用户已评价，添加用户评价信息
    if user_review:
        response['user_review'] = {
            'rating': user_review.rating,
            'comment': user_review.comment,
            'created_at': user_review.created_at.strftime('%Y-%m-%d %H:%M')
        }
    
    return jsonify(response)
