"""
新的仓库路由

此模块定义了新的仓库路由，使用新的仓库模型。
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app.models import User, AdministrativeArea
from app.models_warehouse_new import WarehouseNew
from app import db
from app.forms.warehouse_forms import WarehouseForm

warehouse_new_bp = Blueprint('warehouse_new', __name__)

@warehouse_new_bp.route('/warehouse-new')
@login_required
def index():
    """仓库列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]
    
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    status = request.args.get('status', '')
    area_id = request.args.get('area_id', type=int)
    
    # 构建查询
    query = WarehouseNew.query.filter(WarehouseNew.area_id.in_(area_ids))
    
    # 应用过滤条件
    if status:
        query = query.filter(WarehouseNew.status == status)
    if area_id:
        query = query.filter(WarehouseNew.area_id == area_id)
    
    # 按创建时间降序排序
    query = query.order_by(WarehouseNew.created_at.desc())
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    warehouses = pagination.items
    
    # 获取区域列表，用于筛选
    areas = accessible_areas
    
    return render_template('warehouse_new/index.html',
                          warehouses=warehouses,
                          pagination=pagination,
                          areas=areas,
                          status=status,
                          area_id=area_id)

@warehouse_new_bp.route('/warehouse-new/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建仓库"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    if not accessible_areas:
        flash('您没有权限创建仓库', 'danger')
        return redirect(url_for('warehouse_new.index'))
    
    # 创建表单
    form = WarehouseForm()
    
    # 设置表单选项
    form.area_id.choices = [(area.id, area.name) for area in accessible_areas]
    form.manager_id.choices = [(user.id, user.real_name or user.username) 
                              for user in User.query.filter(User.area_id.in_([area.id for area in accessible_areas])).all()]
    
    if form.validate_on_submit():
        # 检查用户是否有权限在选定区域创建仓库
        if not current_user.can_access_area_by_id(form.area_id.data):
            flash('您没有权限在选定区域创建仓库', 'danger')
            return redirect(url_for('warehouse_new.create'))
        
        # 检查该区域是否已有仓库
        existing_warehouse = WarehouseNew.query.filter_by(area_id=form.area_id.data).first()
        if existing_warehouse:
            flash('该区域已有仓库，不能重复创建', 'warning')
            return redirect(url_for('warehouse_new.index'))
        
        # 创建仓库
        warehouse = WarehouseNew(
            name=form.name.data,
            area_id=form.area_id.data,
            location=form.location.data,
            manager_id=form.manager_id.data,
            capacity=form.capacity.data,
            capacity_unit=form.capacity_unit.data,
            temperature_range=form.temperature_range.data,
            humidity_range=form.humidity_range.data,
            status=form.status.data,
            notes=form.notes.data
        )
        
        try:
            db.session.add(warehouse)
            db.session.commit()
            flash('仓库创建成功', 'success')
            return redirect(url_for('warehouse_new.view', id=warehouse.id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建仓库时出错: {str(e)}")
            flash(f'创建仓库时出错: {str(e)}', 'danger')
    
    return render_template('warehouse_new/form.html',
                          form=form,
                          title='创建仓库')

@warehouse_new_bp.route('/warehouse-new/<int:id>')
@login_required
def view(id):
    """查看仓库详情"""
    warehouse = WarehouseNew.query.get_or_404(id)
    
    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限查看该仓库', 'danger')
        return redirect(url_for('warehouse_new.index'))
    
    return render_template('warehouse_new/view.html',
                          warehouse=warehouse)

@warehouse_new_bp.route('/warehouse-new/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑仓库"""
    warehouse = WarehouseNew.query.get_or_404(id)
    
    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限编辑该仓库', 'danger')
        return redirect(url_for('warehouse_new.index'))
    
    # 创建表单
    form = WarehouseForm(obj=warehouse)
    
    # 设置表单选项
    accessible_areas = current_user.get_accessible_areas()
    form.area_id.choices = [(area.id, area.name) for area in accessible_areas]
    form.manager_id.choices = [(user.id, user.real_name or user.username) 
                              for user in User.query.filter(User.area_id.in_([area.id for area in accessible_areas])).all()]
    
    if form.validate_on_submit():
        # 更新仓库信息
        form.populate_obj(warehouse)
        
        try:
            db.session.commit()
            flash('仓库信息更新成功', 'success')
            return redirect(url_for('warehouse_new.view', id=id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新仓库信息时出错: {str(e)}")
            flash(f'更新仓库信息时出错: {str(e)}', 'danger')
    
    return render_template('warehouse_new/form.html',
                          form=form,
                          warehouse=warehouse,
                          title='编辑仓库')
