from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import StringField, SelectField, DateField, TextAreaField, FloatField, SubmitField, BooleanField, SelectMultipleField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, NumberRange
from datetime import date
from app.models import AdministrativeArea, User, Role

class EmployeeForm(FlaskForm):
    """员工表单"""
    name = StringField('姓名', validators=[DataRequired(message='请输入姓名'), Length(min=2, max=50, message='姓名长度必须在2-50个字符之间')])
    gender = SelectField('性别', choices=[('男', '男'), ('女', '女')], validators=[DataRequired(message='请选择性别')])
    birth_date = DateField('出生日期', format='%Y-%m-%d', default=date.today, validators=[Optional()])
    phone = StringField('联系电话', validators=[DataRequired(message='请输入联系电话'), Length(min=11, max=11, message='请输入11位手机号码')])
    address = StringField('住址', validators=[Optional(), Length(max=200, message='住址长度不能超过200个字符')])
    position = StringField('职位', validators=[DataRequired(message='请输入职位'), Length(max=50, message='职位长度不能超过50个字符')])
    department = StringField('部门', validators=[DataRequired(message='请输入部门'), Length(max=50, message='部门长度不能超过50个字符')])
    area_id = SelectField('所属区域', coerce=int, validators=[Optional()])
    photo = FileField('照片', validators=[Optional(), FileAllowed(['jpg', 'jpeg', 'png'], '只允许上传jpg, jpeg, png格式的图片')])
    status = SelectField('状态', choices=[(1, '在职'), (0, '离职'), (2, '休假')], coerce=int, default=1, validators=[DataRequired(message='请选择状态')])
    entry_date = DateField('入职日期', format='%Y-%m-%d', default=date.today, validators=[DataRequired(message='请选择入职日期')])
    leave_date = DateField('离职日期', format='%Y-%m-%d', validators=[Optional()])

    # 新增字段 - 食品安全责任
    responsible_areas = TextAreaField('负责区域', validators=[Optional(), Length(max=500, message='负责区域长度不能超过500个字符')])
    food_safety_certifications = TextAreaField('食品安全证书', validators=[Optional(), Length(max=500, message='食品安全证书长度不能超过500个字符')])

    # 新增字段 - 系统账号关联
    create_user_account = BooleanField('创建系统账号')
    username = StringField('用户名', validators=[Optional(), Length(min=3, max=20, message='用户名长度必须在3-20个字符之间')])
    password = StringField('密码', default='123456', validators=[Optional(), Length(min=6, message='密码长度不能少于6个字符')])
    roles = SelectMultipleField('系统角色', coerce=int, validators=[Optional()])

    submit = SubmitField('提交')

    def __init__(self, *args, **kwargs):
        super(EmployeeForm, self).__init__(*args, **kwargs)

        # 从 flask_login 导入 current_user
        from flask_login import current_user

        # 获取当前用户可访问的区域
        if current_user.is_authenticated:
            if current_user.is_admin():
                # 管理员可以看到所有区域
                areas = AdministrativeArea.query.filter_by(status=1).order_by(AdministrativeArea.level, AdministrativeArea.name).all()
            else:
                # 普通用户只能看到自己所在区域及其下级区域
                areas = current_user.get_accessible_areas()
        else:
            areas = []

        area_choices = [(0, '请选择区域')]
        for area in areas:
            area_choices.append((area.id, f"{area.get_level_name()} - {area.name}"))
        self.area_id.choices = area_choices

        # 获取所有角色
        roles = Role.query.all()
        self.roles.choices = [(role.id, role.name) for role in roles]

class HealthCertificateForm(FlaskForm):
    """健康证表单"""
    certificate_no = StringField('证件编号', validators=[DataRequired(message='请输入证件编号'), Length(max=50, message='证件编号长度不能超过50个字符')])
    issue_authority = StringField('发证机构', validators=[DataRequired(message='请输入发证机构'), Length(max=100, message='发证机构长度不能超过100个字符')])
    issue_date = DateField('发证日期', format='%Y-%m-%d', validators=[DataRequired(message='请选择发证日期')])
    expire_date = DateField('到期日期', format='%Y-%m-%d', validators=[DataRequired(message='请选择到期日期')])
    certificate_img = FileField('证件图片', validators=[Optional(), FileAllowed(['jpg', 'jpeg', 'png', 'pdf'], '只允许上传jpg, jpeg, png, pdf格式的文件')])
    status = SelectField('状态', choices=[(1, '有效'), (0, '无效')], coerce=int, validators=[DataRequired(message='请选择状态')])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500, message='备注长度不能超过500个字符')])
    submit = SubmitField('提交')

class MedicalExaminationForm(FlaskForm):
    """体检记录表单"""
    exam_date = DateField('体检日期', format='%Y-%m-%d', validators=[DataRequired(message='请选择体检日期')])
    exam_hospital = StringField('体检医院', validators=[DataRequired(message='请输入体检医院'), Length(max=100, message='体检医院长度不能超过100个字符')])
    result = SelectField('体检结果', choices=[('合格', '合格'), ('不合格', '不合格')], validators=[DataRequired(message='请选择体检结果')])
    report_img = FileField('体检报告', validators=[Optional(), FileAllowed(['jpg', 'jpeg', 'png', 'pdf'], '只允许上传jpg, jpeg, png, pdf格式的文件')])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500, message='备注长度不能超过500个字符')])
    submit = SubmitField('提交')

class DailyHealthCheckForm(FlaskForm):
    """日常健康检查表单"""
    check_date = DateField('检查日期', format='%Y-%m-%d', default=date.today, validators=[DataRequired(message='请选择检查日期')])
    temperature = FloatField('体温', validators=[DataRequired(message='请输入体温'), NumberRange(min=35.0, max=42.0, message='体温必须在35.0-42.0之间')])
    health_status = SelectField('健康状态', choices=[('正常', '正常'), ('异常', '异常')], validators=[DataRequired(message='请选择健康状态')])
    symptoms = TextAreaField('症状描述', validators=[Optional(), Length(max=500, message='症状描述长度不能超过500个字符')])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500, message='备注长度不能超过500个字符')])
    submit = SubmitField('提交')

class TrainingRecordForm(FlaskForm):
    """培训记录表单"""
    training_name = StringField('培训名称', validators=[DataRequired(message='请输入培训名称'), Length(max=100, message='培训名称长度不能超过100个字符')])
    training_date = DateField('培训日期', format='%Y-%m-%d', validators=[DataRequired(message='请选择培训日期')])
    expire_date = DateField('有效期', format='%Y-%m-%d', validators=[Optional()])
    certificate_no = StringField('证书编号', validators=[Optional(), Length(max=50, message='证书编号长度不能超过50个字符')])
    certificate_img = FileField('证书图片', validators=[Optional(), FileAllowed(['jpg', 'jpeg', 'png', 'pdf'], '只允许上传jpg, jpeg, png, pdf格式的文件')])
    score = FloatField('培训成绩', validators=[Optional(), NumberRange(min=0, max=100, message='成绩必须在0-100之间')])
    trainer = StringField('培训师', validators=[Optional(), Length(max=50, message='培训师长度不能超过50个字符')])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500, message='备注长度不能超过500个字符')])
    submit = SubmitField('提交')
