"""
通知发送示例代码
展示如何在项目中使用通知服务
"""

from app.services.notification_service import NotificationService
from app.models import User
from flask_login import current_user

# ===== 基础通知发送示例 =====

def example_send_basic_notification():
    """基础通知发送示例"""
    
    # 1. 发送给指定用户
    user_id = 1
    notification = NotificationService.send_to_user(
        user_id=user_id,
        title="系统维护通知",
        content="系统将于今晚22:00-24:00进行维护，期间可能无法正常使用，请提前做好准备。",
        notification_type=NotificationService.TYPE_SYSTEM,
        level=NotificationService.LEVEL_IMPORTANT
    )
    
    if notification:
        print(f"通知发送成功，通知ID: {notification.id}")
    else:
        print("通知发送失败")

def example_send_to_multiple_users():
    """发送给多个用户示例"""
    
    user_ids = [1, 2, 3, 4, 5]
    notifications = NotificationService.send_to_users(
        user_ids=user_ids,
        title="新功能上线通知",
        content="库存管理模块新增了批次管理功能，现在可以更精确地跟踪食材的批次信息。",
        notification_type=NotificationService.TYPE_SYSTEM,
        level=NotificationService.LEVEL_NORMAL
    )
    
    print(f"成功发送 {len(notifications)} 条通知")

def example_send_to_area():
    """发送给区域用户示例"""
    
    area_id = 1  # 假设区域ID为1
    notifications = NotificationService.send_to_area(
        area_id=area_id,
        title="区域会议通知",
        content="本区域将于明天下午2点召开食品安全工作会议，请相关人员准时参加。",
        notification_type=NotificationService.TYPE_TASK,
        level=NotificationService.LEVEL_IMPORTANT,
        include_sub_areas=True  # 包含子区域
    )
    
    print(f"向区域发送通知，共 {len(notifications)} 个用户收到")

# ===== 业务场景通知示例 =====

def example_inventory_alert():
    """库存不足预警示例"""
    
    # 假设检测到库存不足
    user_id = current_user.id if current_user.is_authenticated else 1
    
    notification = NotificationService.send_inventory_alert(
        user_id=user_id,
        ingredient_name="大米",
        current_stock=50.0,
        min_stock=100.0,
        unit="kg",
        warehouse_name="主仓库"
    )
    
    if notification:
        print("库存不足预警发送成功")

def example_expiry_alert():
    """食材过期预警示例"""
    
    user_id = current_user.id if current_user.is_authenticated else 1
    
    # 即将过期的食材
    notification1 = NotificationService.send_expiry_alert(
        user_id=user_id,
        ingredient_name="牛奶",
        batch_number="B20241201001",
        expiry_date="2024-12-15",
        days_left=3,
        warehouse_name="冷藏仓库"
    )
    
    # 已过期的食材
    notification2 = NotificationService.send_expiry_alert(
        user_id=user_id,
        ingredient_name="面包",
        batch_number="B20241201002",
        expiry_date="2024-12-10",
        days_left=0,  # 已过期
        warehouse_name="常温仓库"
    )
    
    print("过期预警发送完成")

def example_health_cert_alert():
    """健康证过期预警示例"""
    
    user_id = current_user.id if current_user.is_authenticated else 1
    
    notification = NotificationService.send_health_cert_alert(
        user_id=user_id,
        employee_name="张三",
        cert_number="HC20240101001",
        expiry_date="2024-12-31",
        days_left=15
    )
    
    if notification:
        print("健康证过期预警发送成功")

def example_purchase_order_notification():
    """采购订单状态通知示例"""
    
    user_id = current_user.id if current_user.is_authenticated else 1
    
    notification = NotificationService.send_purchase_order_notification(
        user_id=user_id,
        order_number="PO20241201001",
        supplier_name="优质食材供应商",
        status="已确认"
    )
    
    if notification:
        print("采购订单状态通知发送成功")

def example_stock_in_notification():
    """入库单状态通知示例"""
    
    user_id = current_user.id if current_user.is_authenticated else 1
    
    notification = NotificationService.send_stock_in_notification(
        user_id=user_id,
        stock_in_number="SI20241201001",
        warehouse_name="主仓库",
        status="已审核"
    )
    
    if notification:
        print("入库单状态通知发送成功")

def example_menu_plan_notification():
    """菜单计划通知示例"""
    
    area_id = 1  # 假设区域ID为1
    
    notifications = NotificationService.send_menu_plan_notification(
        area_id=area_id,
        plan_date="2024-12-15",
        meal_type="午餐",
        status="已发布"
    )
    
    print(f"菜单计划通知发送完成，共 {len(notifications)} 个用户收到")

# ===== 在实际业务代码中的使用示例 =====

def example_in_stock_in_route():
    """在入库单路由中使用通知的示例"""
    
    # 假设这是在入库单审核通过后的代码
    def approve_stock_in(stock_in_id):
        from app.models import StockIn
        
        stock_in = StockIn.query.get(stock_in_id)
        if stock_in:
            # 更新状态
            stock_in.status = '已审核'
            
            # 发送通知给创建者
            NotificationService.send_stock_in_notification(
                user_id=stock_in.created_by,
                stock_in_number=stock_in.stock_in_number,
                warehouse_name=stock_in.warehouse.name,
                status='已审核'
            )
            
            # 如果是重要入库单，也通知区域管理员
            if stock_in.total_amount > 10000:  # 假设金额超过1万元
                NotificationService.send_to_area(
                    area_id=stock_in.warehouse.area_id,
                    title=f"大额入库单审核通过 - {stock_in.stock_in_number}",
                    content=f"入库单{stock_in.stock_in_number}（金额：{stock_in.total_amount}元）已审核通过。",
                    notification_type=NotificationService.TYPE_STOCK_IN,
                    level=NotificationService.LEVEL_IMPORTANT
                )

def example_in_inventory_check():
    """在库存检查中使用通知的示例"""
    
    def check_inventory_alerts():
        from app.models import Inventory, InventoryAlert
        from datetime import date, timedelta
        
        # 检查库存不足
        alerts = InventoryAlert.query.filter_by(alert_type='库存不足').all()
        for alert in alerts:
            current_stock = Inventory.query.filter_by(
                ingredient_id=alert.ingredient_id,
                warehouse_id=alert.warehouse_id
            ).with_entities(Inventory.quantity.label('total')).scalar() or 0
            
            if current_stock <= alert.min_quantity:
                # 发送库存不足预警
                NotificationService.send_inventory_alert(
                    user_id=alert.created_by,
                    ingredient_name=alert.ingredient.name,
                    current_stock=current_stock,
                    min_stock=alert.min_quantity,
                    unit=alert.ingredient.unit,
                    warehouse_name=alert.warehouse.name
                )
        
        # 检查即将过期的食材
        expiry_date = date.today() + timedelta(days=7)  # 7天内过期
        expiring_items = Inventory.query.filter(
            Inventory.expiry_date <= expiry_date,
            Inventory.expiry_date > date.today(),
            Inventory.quantity > 0
        ).all()
        
        for item in expiring_items:
            days_left = (item.expiry_date - date.today()).days
            NotificationService.send_expiry_alert(
                user_id=item.warehouse.manager_id or 1,  # 发送给仓库管理员
                ingredient_name=item.ingredient.name,
                batch_number=item.batch_number,
                expiry_date=item.expiry_date.strftime('%Y-%m-%d'),
                days_left=days_left,
                warehouse_name=item.warehouse.name
            )

# ===== 定时任务中的通知示例 =====

def example_daily_notification_task():
    """每日通知任务示例（可以配合定时任务使用）"""
    
    from datetime import date, timedelta
    
    # 每日早上发送今日工作提醒
    today = date.today()
    
    # 发送给所有管理员
    managers = User.query.filter(User.roles.any(name='manager')).all()
    for manager in managers:
        NotificationService.send_to_user(
            user_id=manager.id,
            title=f"每日工作提醒 - {today.strftime('%Y年%m月%d日')}",
            content="请及时查看今日的菜单计划、库存状态和待处理事项。",
            notification_type=NotificationService.TYPE_TASK,
            level=NotificationService.LEVEL_NORMAL
        )
    
    print("每日工作提醒发送完成")

# ===== 使用说明 =====

"""
通知发送的几种方式：

1. 手动发送（通过Web界面）：
   访问 /notifications/send 页面，填写表单发送通知

2. 代码中发送（推荐）：
   from app.services.notification_service import NotificationService
   
   # 发送给单个用户
   NotificationService.send_to_user(user_id, title, content, type, level)
   
   # 发送给多个用户
   NotificationService.send_to_users(user_ids, title, content, type, level)
   
   # 发送给区域用户
   NotificationService.send_to_area(area_id, title, content, type, level)
   
   # 发送给所有用户
   NotificationService.send_to_all(title, content, type, level)

3. 业务场景专用方法：
   # 库存预警
   NotificationService.send_inventory_alert(...)
   
   # 过期预警
   NotificationService.send_expiry_alert(...)
   
   # 健康证预警
   NotificationService.send_health_cert_alert(...)
   
   # 采购订单通知
   NotificationService.send_purchase_order_notification(...)

4. 通知类型：
   - TYPE_SYSTEM: 系统通知
   - TYPE_HEALTH_CERT: 健康证提醒
   - TYPE_MENU: 食谱通知
   - TYPE_PURCHASE: 采购通知
   - TYPE_INSPECTION: 检查通知
   - TYPE_TASK: 任务通知
   - TYPE_INVENTORY: 库存通知
   - TYPE_EXPIRY: 过期提醒
   - TYPE_STOCK_IN: 入库通知
   - TYPE_CONSUMPTION: 消耗通知

5. 通知级别：
   - LEVEL_NORMAL (0): 普通
   - LEVEL_IMPORTANT (1): 重要
   - LEVEL_URGENT (2): 紧急
"""
