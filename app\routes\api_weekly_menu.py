"""
周菜单API路由模块
提供周菜单相关的API接口
"""

from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models import WeeklyMenu, WeeklyMenuRecipe, AdministrativeArea, Recipe, RecipeCategory
from app.utils.decorators import check_permission, handle_weekly_menu_error
from app.services.weekly_menu_service import WeeklyMenuService
from datetime import datetime, date, timedelta
from app import db
import json

api_weekly_menu_bp = Blueprint('api_weekly_menu', __name__)

@api_weekly_menu_bp.route('/api/weekly-menu/week/save', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def save_weekly_menu():
    """保存周菜单API"""
    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据为空'
        })

    area_id = data.get('area_id')
    week_start_str = data.get('week_start')
    menu_data = data.get('menu_data', {})

    # 验证必要参数
    if not area_id or not week_start_str:
        return jsonify({
            'success': False,
            'message': '缺少必要参数: area_id 或 week_start'
        })

    # 检查用户是否有权限访问该区域
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限访问该区域'
        })

    # 获取或创建周菜单
    menu = WeeklyMenuService.get_menu(area_id, week_start_str)
    if menu:
        weekly_menu_id = menu.id
    else:
        weekly_menu_id = WeeklyMenuService.create_menu(area_id, week_start_str, current_user.id)

    # 保存菜单数据
    WeeklyMenuService.save_menu(weekly_menu_id, menu_data)

    # 获取保存后的菜单对象
    weekly_menu = WeeklyMenu.query.get(weekly_menu_id)

    return jsonify({
        'success': True,
        'message': '周菜单保存成功',
        'weekly_menu_id': weekly_menu_id,
        'status': weekly_menu.status
    })

@api_weekly_menu_bp.route('/api/weekly-menu/create', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
def create_menu():
    """创建周菜单API"""
    try:
        # 获取请求数据
        current_app.logger.info(f"收到创建周菜单请求: {request.data}")
        data = request.get_json()
        if not data:
            current_app.logger.warning("创建周菜单请求数据为空")
            return jsonify({
                'success': False,
                'message': '请求数据为空'
            })

        area_id = data.get('area_id')
        week_start = data.get('week_start')
        current_app.logger.info(f"创建周菜单参数: area_id={area_id}, week_start={week_start}")

        # 验证必要参数
        if not area_id or not week_start:
            current_app.logger.warning(f"创建周菜单缺少必要参数: area_id={area_id}, week_start={week_start}")
            return jsonify({
                'success': False,
                'message': '缺少必要参数: area_id 或 week_start'
            })

        # 检查用户是否有权限访问该区域
        current_app.logger.info(f"检查用户权限: user_id={current_user.id}, area_id={area_id}")
        current_app.logger.info(f"用户角色: {[role.name for role in current_user.roles]}")

        is_admin = current_user.is_admin()
        is_school_admin = current_user.has_role('学校管理员')
        can_access_area = current_user.can_access_area_by_id(area_id)
        has_edit_permission = current_user.has_permission('weekly_menu', 'edit')

        current_app.logger.info(f"权限检查结果: is_admin={is_admin}, is_school_admin={is_school_admin}, can_access_area={can_access_area}, has_edit_permission={has_edit_permission}")

        if not (is_admin or is_school_admin or can_access_area):
            current_app.logger.warning(f"用户 {current_user.id} 没有权限访问区域 {area_id}")
            return jsonify({
                'success': False,
                'message': '您没有权限访问该区域'
            })

        if not has_edit_permission:
            current_app.logger.warning(f"用户 {current_user.id} 没有周菜单编辑权限")
            return jsonify({
                'success': False,
                'message': '您没有周菜单编辑权限'
            })

        # 创建周菜单
        current_app.logger.info(f"开始创建周菜单: area_id={area_id}, week_start={week_start}, created_by={current_user.id}")
        weekly_menu_id = WeeklyMenuService.create_menu(area_id, week_start, current_user.id)
        current_app.logger.info(f"周菜单创建成功: id={weekly_menu_id}")

        # 获取创建后的菜单对象
        weekly_menu = WeeklyMenu.query.get(weekly_menu_id)
        if not weekly_menu:
            current_app.logger.error(f"无法获取刚创建的周菜单: id={weekly_menu_id}")
            return jsonify({
                'success': False,
                'message': '菜单创建成功但无法获取菜单对象'
            })

        response_data = {
            'success': True,
            'message': '周菜单创建成功',
            'weekly_menu_id': weekly_menu_id,
            'status': weekly_menu.status
        }
        current_app.logger.info(f"返回创建周菜单响应: {response_data}")
        return jsonify(response_data)
    except Exception as e:
        current_app.logger.exception(f"创建周菜单时发生异常: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'创建周菜单失败: {str(e)}'
        })

@api_weekly_menu_bp.route('/api/weekly-menu/<int:menu_id>/publish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def publish_menu(menu_id):
    """发布周菜单API"""
    # 发布菜单
    WeeklyMenuService.publish_menu(menu_id)

    # 获取发布后的菜单对象
    weekly_menu = WeeklyMenu.query.get(menu_id)

    return jsonify({
        'success': True,
        'message': '周菜单发布成功',
        'weekly_menu_id': menu_id,
        'status': weekly_menu.status
    })

@api_weekly_menu_bp.route('/api/weekly-menu/<int:menu_id>/unpublish', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def unpublish_menu(menu_id):
    """取消发布周菜单API"""
    # 取消发布菜单
    WeeklyMenuService.unpublish_menu(menu_id)

    # 获取取消发布后的菜单对象
    weekly_menu = WeeklyMenu.query.get(menu_id)

    return jsonify({
        'success': True,
        'message': '周菜单取消发布成功',
        'weekly_menu_id': menu_id,
        'status': weekly_menu.status
    })

@api_weekly_menu_bp.route('/api/weekly-menu/copy', methods=['POST'])
@login_required
@check_permission('weekly_menu', 'edit')
@handle_weekly_menu_error
def copy_menu():
    """复制周菜单API"""
    # 获取请求数据
    data = request.get_json()
    if not data:
        return jsonify({
            'success': False,
            'message': '请求数据为空'
        })

    source_menu_id = data.get('source_menu_id')
    target_week_start = data.get('target_week_start')
    area_id = data.get('area_id')

    # 验证必要参数
    if not source_menu_id or not target_week_start or not area_id:
        return jsonify({
            'success': False,
            'message': '缺少必要参数: source_menu_id, target_week_start 或 area_id'
        })

    # 检查用户是否有权限访问该区域
    if not current_user.is_admin() and not current_user.has_role('学校管理员') and not current_user.can_access_area_by_id(area_id):
        return jsonify({
            'success': False,
            'message': '您没有权限访问该区域'
        })

    # 复制菜单
    new_menu_id = WeeklyMenuService.copy_menu(source_menu_id, target_week_start, area_id)

    # 获取复制后的菜单对象
    weekly_menu = WeeklyMenu.query.get(new_menu_id)

    return jsonify({
        'success': True,
        'message': '周菜单复制成功',
        'weekly_menu_id': new_menu_id,
        'status': weekly_menu.status
    })

@api_weekly_menu_bp.route('/api/recipes', methods=['GET'])
@login_required
def get_recipes():
    """获取菜谱列表API"""
    # 获取查询参数
    category = request.args.get('category')
    keyword = request.args.get('keyword')

    # 获取当前用户的主要区域
    user_area = current_user.get_current_area()

    # 构建查询 - 优先显示学校专用食谱，系统食谱作为备选
    query = Recipe.query.filter(
        Recipe.status == 1,
        db.or_(
            Recipe.area_id == user_area.id if user_area else None,  # 本校专用食谱
            Recipe.is_global == True,      # 系统食谱（可复制使用）
            Recipe.area_id.is_(None)       # 兼容旧数据（无area_id的食谱）
        )
    ).order_by(
        # 优先显示学校专用食谱
        db.case(
            (Recipe.area_id == (user_area.id if user_area else None), 1),
            (Recipe.is_global == True, 2),
            else_=3
        ),
        Recipe.priority.desc(),
        Recipe.id.desc()
    )

    # 应用筛选条件
    if category and category != 'all':
        query = query.filter_by(category=category)

    if keyword:
        query = query.filter(Recipe.name.like(f'%{keyword}%'))

    # 获取菜谱列表
    recipes = query.order_by(Recipe.priority.desc(), Recipe.id.desc()).all()

    # 按分类分组
    recipes_by_category = {}
    for recipe in recipes:
        if recipe.category not in recipes_by_category:
            recipes_by_category[recipe.category] = []

        recipes_by_category[recipe.category].append({
            'id': recipe.id,
            'name': recipe.name,
            'category': recipe.category,
            'image': recipe.main_image
        })

    return jsonify({
        'success': True,
        'data': {
            'recipes_by_category': recipes_by_category
        }
    })

@api_weekly_menu_bp.route('/api/recipe-categories', methods=['GET'])
@login_required
def get_recipe_categories():
    """获取菜谱分类列表API"""
    # 获取分类列表
    categories = RecipeCategory.query.all()

    return jsonify({
        'success': True,
        'data': {
            'categories': [category.to_dict() for category in categories]
        }
    })
