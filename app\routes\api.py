"""
API路由模块
提供系统的API接口
"""

from flask import Blueprint, request, jsonify, current_app, url_for
from flask_login import login_required, current_user
from app.models import (
    WeeklyMenu, WeeklyMenuRecipe, Recipe, RecipeIngredient,
    ConsumptionPlan, ConsumptionDetail, StockOut, StockOutItem, Inventory,
    StockIn, StockInItem, PurchaseOrder, PurchaseOrderItem, Supplier, Ingredient,
    FoodSample
)
from app.utils.food_trace import trace_food_supply_chain
from datetime import datetime, date, timedelta
from app.utils.decorators import check_permission

api_bp = Blueprint('food_trace_api', __name__)

@api_bp.route('/food-trace', methods=['GET'])
@login_required
def food_trace():
    """食材溯源API"""
    date_str = request.args.get('date')
    meal_type = request.args.get('meal_type')
    area_id = request.args.get('area_id', type=int)

    if not date_str or not meal_type:
        return jsonify({
            'success': False,
            'message': '缺少必要参数'
        })

    try:
        # 检查用户权限
        if area_id and not current_user.can_access_area_by_id(area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的数据'
            })

        # 调用溯源函数
        trace_data = trace_food_supply_chain(date_str, meal_type, area_id)

        return jsonify(trace_data)
    except Exception as e:
        current_app.logger.error(f"食材溯源失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'溯源失败: {str(e)}'
        })

@api_bp.route('/menu-recipes', methods=['GET'])
@login_required
def get_menu_recipes():
    """获取指定日期和餐次的菜谱，支持分页"""
    date_str = request.args.get('date')
    meal_type = request.args.get('meal_type')
    area_id = request.args.get('area_id', type=int)
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 9, type=int)  # 默认每页9个菜品

    if not date_str or not meal_type:
        return jsonify({
            'success': False,
            'message': '缺少必要参数'
        })

    try:
        # 将字符串日期转换为日期对象
        menu_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 检查用户权限
        if area_id and not current_user.can_access_area_by_id(area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的数据'
            })

        all_recipes = []
        menu_type = None
        menu_id = None
        area_name = None

        # 直接从周菜单中查找食谱安排
        weekday = menu_date.weekday()  # 0-6，0表示周一
        week_start = menu_date - timedelta(days=weekday)
        week_end = week_start + timedelta(days=6)

        weekly_menu_query = WeeklyMenu.query.filter(
            WeeklyMenu.week_start <= menu_date,
            WeeklyMenu.week_end >= menu_date,
            WeeklyMenu.status == '已发布'
        )

        if area_id:
            weekly_menu_query = weekly_menu_query.filter(WeeklyMenu.area_id == area_id)

        weekly_menu = weekly_menu_query.first()

        if weekly_menu:
            # 从周菜单中获取菜谱
            weekly_recipes = WeeklyMenuRecipe.query.filter(
                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                WeeklyMenuRecipe.day_of_week == weekday + 1,  # 数据库中1-7表示周一到周日
                WeeklyMenuRecipe.meal_type == meal_type
            ).all()

            for weekly_recipe in weekly_recipes:
                if weekly_recipe.recipe:
                    # 检查是否已有留样记录
                    has_sample = FoodSample.query.filter(
                        FoodSample.recipe_id == weekly_recipe.recipe.id,
                        FoodSample.meal_date == menu_date,
                        FoodSample.meal_type == meal_type,
                        FoodSample.area_id == area_id if area_id else True
                    ).first() is not None

                    recipe_data = {
                        'id': weekly_recipe.recipe.id,
                        'name': weekly_recipe.recipe.name,
                        'category': weekly_recipe.recipe.category,
                        'image': None,  # 不显示图片
                        'description': weekly_recipe.recipe.description,
                        'source': 'weekly_menu',
                        'has_sample': has_sample
                    }
                    all_recipes.append(recipe_data)

            menu_type = 'weekly_menu'
            menu_id = weekly_menu.id
            area_name = weekly_menu.area.name if weekly_menu.area else None
        else:
            # 日菜单功能已移除，如果没有找到周菜单则返回空
            return jsonify({
                'success': False,
                'message': '未找到指定日期和餐次的菜单'
            })

        # 计算总页数
        total_recipes = len(all_recipes)
        total_pages = (total_recipes + per_page - 1) // per_page

        # 分页处理
        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total_recipes)
        paged_recipes = all_recipes[start_idx:end_idx]

        # 检查是否有留样记录
        food_samples = FoodSample.query.filter(
            FoodSample.meal_date == menu_date,
            FoodSample.meal_type == meal_type
        )

        if area_id:
            food_samples = food_samples.filter(FoodSample.area_id == area_id)

        food_samples = food_samples.all()

        return jsonify({
            'success': True,
            'data': {
                'menu_type': menu_type,
                'menu_id': menu_id,
                'area_id': area_id,
                'area_name': area_name,
                'date': date_str,
                'meal_type': meal_type,
                'recipes': paged_recipes,
                'total_recipes': total_recipes,
                'total_pages': total_pages,
                'current_page': page,
                'has_samples': len(food_samples) > 0
            }
        })

    except Exception as e:
        current_app.logger.error(f"获取菜谱失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取菜谱失败: {str(e)}'
        })

@api_bp.route('/food-samples', methods=['GET'])
@login_required
def get_food_samples():
    """获取指定日期和餐次的留样记录"""
    date_str = request.args.get('date')
    meal_type = request.args.get('meal_type')
    area_id = request.args.get('area_id', type=int)

    if not date_str or not meal_type:
        return jsonify({
            'success': False,
            'message': '缺少必要参数'
        })

    try:
        # 将字符串日期转换为日期对象
        sample_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 检查用户权限
        if area_id and not current_user.can_access_area_by_id(area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的数据'
            })

        # 查询留样记录
        query = FoodSample.query.filter(
            FoodSample.meal_date == sample_date,
            FoodSample.meal_type == meal_type
        )

        if area_id:
            query = query.filter(FoodSample.area_id == area_id)

        food_samples = query.all()

        samples = []
        for sample in food_samples:
            sample_data = {
                'id': sample.id,
                'sample_number': sample.sample_number,
                'recipe_id': sample.recipe_id,
                'recipe_name': sample.recipe.name if sample.recipe else None,
                'area_id': sample.area_id,
                'area_name': sample.area.name if sample.area else None,
                'menu_plan_id': sample.menu_plan_id,
                'meal_date': sample.meal_date.strftime('%Y-%m-%d') if sample.meal_date else None,
                'meal_type': sample.meal_type,
                'sample_image': sample.sample_image,
                'sample_quantity': float(sample.sample_quantity) if sample.sample_quantity else 0,
                'sample_unit': sample.sample_unit,
                'storage_location': sample.storage_location,
                'storage_temperature': sample.storage_temperature,
                'start_time': sample.start_time.strftime('%Y-%m-%d %H:%M:%S') if sample.start_time else None,
                'end_time': sample.end_time.strftime('%Y-%m-%d %H:%M:%S') if sample.end_time else None,
                'status': sample.status
            }
            samples.append(sample_data)

        return jsonify({
            'success': True,
            'data': samples
        })

    except Exception as e:
        current_app.logger.error(f"获取留样记录失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取留样记录失败: {str(e)}'
        })

@api_bp.route('/recipe/<int:recipe_id>', methods=['GET'])
@login_required
def get_recipe(recipe_id):
    """获取食谱详情"""
    try:
        recipe = Recipe.query.get_or_404(recipe_id)

        # 获取食谱材料
        ingredients = RecipeIngredient.query.filter_by(recipe_id=recipe_id).all()

        # 构建食谱数据
        recipe_data = {
            'id': recipe.id,
            'name': recipe.name,
            'category': recipe.category,
            'image': url_for('static', filename=recipe.image) if recipe.image else None,
            'description': recipe.description,
            'ingredients': []
        }

        # 添加食谱材料
        for ingredient in ingredients:
            ingredient_data = {
                'id': ingredient.id,
                'name': ingredient.ingredient.name if ingredient.ingredient else '未知食材',
                'quantity': float(ingredient.quantity) if ingredient.quantity else 0,
                'unit': ingredient.unit
            }
            recipe_data['ingredients'].append(ingredient_data)

        return jsonify({
            'success': True,
            'data': recipe_data
        })

    except Exception as e:
        current_app.logger.error(f"获取食谱详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取食谱详情失败: {str(e)}'
        })

@api_bp.route('/purchase-orders/<int:order_id>', methods=['GET'])
@login_required
def get_purchase_order(order_id):
    """获取采购订单详情"""
    try:
        # 获取采购订单
        order = PurchaseOrder.query.get_or_404(order_id)

        # 简化权限设置，允许所有登录用户查看订单

        # 获取订单项目
        order_items = PurchaseOrderItem.query.filter_by(purchase_order_id=order_id).all()

        # 构建订单数据
        items = []
        for item in order_items:
            ingredient = Ingredient.query.get(item.ingredient_id) if item.ingredient_id else None
            item_data = {
                'id': item.id,
                'name': ingredient.name if ingredient else '未知食材',
                'quantity': float(item.quantity) if item.quantity else 0,
                'unit': ingredient.unit if ingredient else '',
                'price': float(item.price) if item.price else 0,
                'category': ingredient.category if ingredient else '',
                'notes': item.notes
            }
            items.append(item_data)

        # 获取供应商和仓库信息
        supplier = Supplier.query.get(order.supplier_id) if order.supplier_id else None
        warehouse = None
        if hasattr(order, 'warehouse_id') and order.warehouse_id:
            from app.models import Warehouse
            warehouse = Warehouse.query.get(order.warehouse_id)

        order_data = {
            'id': order.id,
            'order_number': order.order_number,
            'order_date': order.order_date.strftime('%Y-%m-%d') if order.order_date else None,
            'status': order.status,
            'total_amount': float(order.total_amount) if order.total_amount else 0,
            'notes': order.notes,
            'supplier_name': supplier.name if supplier else '未知供应商',
            'warehouse_name': warehouse.name if warehouse else '未指定仓库',
            'items': items
        }

        return jsonify(order_data)

    except Exception as e:
        current_app.logger.error(f"获取采购订单详情失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取采购订单详情失败: {str(e)}'
        }), 500
