from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Ingredient, Supplier, AdministrativeArea, User, AuditLog
from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow
from app import db
from datetime import datetime, date, timedelta
import json
import uuid
import os
from werkzeug.utils import secure_filename
from sqlalchemy import func

material_batch_bp = Blueprint('material_batch', __name__)

@material_batch_bp.route('/material-batch')
@login_required
def index():
    """批次列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    ingredient_id = request.args.get('ingredient_id', type=int)
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status', '')
    expiry_days = request.args.get('expiry_days', type=int)
    area_id = request.args.get('area_id', type=int)

    # 构建查询
    query = MaterialBatch.query.filter(MaterialBatch.area_id.in_(area_ids))

    # 应用过滤条件
    if ingredient_id:
        query = query.filter(MaterialBatch.ingredient_id == ingredient_id)
    if supplier_id:
        query = query.filter(MaterialBatch.supplier_id == supplier_id)
    if status:
        query = query.filter(MaterialBatch.status == status)
    if expiry_days:
        expiry_date = date.today() + timedelta(days=expiry_days)
        query = query.filter(MaterialBatch.expiry_date <= expiry_date)
    if area_id:
        query = query.filter(MaterialBatch.area_id == area_id)

    # 按创建时间降序排序
    query = query.order_by(MaterialBatch.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    batches = pagination.items

    # 获取所有食材和供应商，用于过滤
    ingredients = Ingredient.query.all()
    suppliers = Supplier.query.all()
    areas = accessible_areas

    return render_template('material_batch/index.html',
                          batches=batches,
                          pagination=pagination,
                          ingredients=ingredients,
                          suppliers=suppliers,
                          areas=areas,
                          ingredient_id=ingredient_id,
                          supplier_id=supplier_id,
                          status=status,
                          expiry_days=expiry_days,
                          area_id=area_id)

@material_batch_bp.route('/material-batch/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建批次"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    if request.method == 'POST':
        # 获取表单数据
        ingredient_id = request.form.get('ingredient_id', type=int)
        supplier_id = request.form.get('supplier_id', type=int)
        production_date = request.form.get('production_date')
        expiry_date = request.form.get('expiry_date')
        production_batch_no = request.form.get('production_batch_no')
        origin_place = request.form.get('origin_place')
        inspection_no = request.form.get('inspection_no')
        certificate_no = request.form.get('certificate_no')
        initial_quantity = request.form.get('initial_quantity', type=float)
        unit = request.form.get('unit')
        unit_price = request.form.get('unit_price', type=float)
        area_id = request.form.get('area_id', type=int)
        remark = request.form.get('remark')

        # 验证必填字段
        if not all([ingredient_id, supplier_id, production_date, expiry_date, initial_quantity, unit, area_id]):
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('material_batch.create'))

        # 检查用户是否有权限操作该区域
        if area_id not in area_ids:
            flash('您没有权限操作该区域', 'danger')
            return redirect(url_for('material_batch.index'))

        # 生成批次编号
        batch_number = f"B{datetime.now().strftime('%Y%m%d%H%M%S')}-{ingredient_id}-{supplier_id}"

        # 创建批次记录
        batch = MaterialBatch(
            batch_number=batch_number,
            ingredient_id=ingredient_id,
            supplier_id=supplier_id,
            production_date=datetime.strptime(production_date, '%Y-%m-%d').date(),
            expiry_date=datetime.strptime(expiry_date, '%Y-%m-%d').date(),
            production_batch_no=production_batch_no,
            origin_place=origin_place,
            inspection_no=inspection_no,
            certificate_no=certificate_no,
            initial_quantity=initial_quantity,
            current_quantity=initial_quantity,
            unit=unit,
            unit_price=unit_price,
            area_id=area_id,
            remark=remark
        )

        db.session.add(batch)

        # 添加批次流水记录
        batch_flow = BatchFlow(
            batch_id=batch.id,
            flow_type='入库',
            flow_direction='增加',
            quantity=initial_quantity,
            unit=unit,
            operator_id=current_user.id,
            flow_date=datetime.now(),
            remark='初始入库'
        )

        db.session.add(batch_flow)

        # 添加审计日志
        from app.utils.log_activity import log_activity
        log_activity(
            action='create',
            resource_type='MaterialBatch',
            resource_id=batch.id,
            details=batch.to_dict()
        )

        db.session.commit()
        flash('批次创建成功！', 'success')
        return redirect(url_for('material_batch.index'))

    # GET 请求，显示创建表单
    ingredients = Ingredient.query.all()
    suppliers = Supplier.query.all()
    areas = accessible_areas

    return render_template('material_batch/form.html',
                          batch=None,
                          ingredients=ingredients,
                          suppliers=suppliers,
                          areas=areas)

@material_batch_bp.route('/material-batch/<int:id>')
@login_required
def view(id):
    """查看批次详情"""
    batch = MaterialBatch.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限查看该批次', 'danger')
        return redirect(url_for('material_batch.index'))

    # 获取批次文档
    documents = TraceDocument.query.filter_by(batch_id=id).all()

    # 获取批次流水
    flows = BatchFlow.query.filter_by(batch_id=id).order_by(BatchFlow.flow_date.desc()).all()

    return render_template('material_batch/view.html',
                          batch=batch,
                          documents=documents,
                          flows=flows)
