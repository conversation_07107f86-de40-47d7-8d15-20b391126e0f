from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, FloatField, SubmitField, DateField, FileField, IntegerField, HiddenField
from wtforms.validators import DataRequired, Email, Optional, Length, NumberRange
from flask_wtf.file import FileAllowed
from datetime import date

class SupplierForm(FlaskForm):
    """供应商表单"""
    name = StringField('供应商名称', validators=[DataRequired(message='请输入供应商名称'), Length(min=2, max=100)])
    category_id = SelectField('供应商分类', coerce=int, validators=[Optional()])
    contact_person = StringField('联系人', validators=[DataRequired(message='请输入联系人'), Length(min=2, max=50)])
    phone = StringField('联系电话', validators=[DataRequired(message='请输入联系电话'), Length(min=5, max=20)])
    email = StringField('电子邮箱', validators=[Optional(), Email(message='请输入有效的电子邮箱')], default='')
    address = StringField('地址', validators=[DataRequired(message='请输入地址'), Length(min=5, max=200)])
    business_license = StringField('营业执照号', validators=[DataRequired(message='请输入营业执照号'), Length(min=5, max=200)])
    tax_id = StringField('税务登记号', validators=[Optional(), Length(max=50)], default='')
    bank_name = StringField('开户银行', validators=[Optional(), Length(max=100)], default='')
    bank_account = StringField('银行账号', validators=[Optional(), Length(max=50)], default='')
    rating = FloatField('评级', validators=[Optional(), NumberRange(min=0, max=5)], default=3.0)
    status = SelectField('状态', choices=[(1, '合作中'), (0, '已停用')], coerce=int, default=1)

    # 学校绑定相关字段（在编辑模式下这些字段将被设置为可选）
    area_id = SelectField('合作学校', coerce=int, validators=[Optional()])
    contract_number = StringField('合同编号', validators=[Optional(), Length(max=100)], default='')
    start_date = DateField('合作开始日期', validators=[Optional()], default=date.today)
    end_date = DateField('合作结束日期', validators=[Optional()])
    relation_status = SelectField('合作状态', choices=[(1, '有效'), (0, '已终止')], coerce=int, default=1)
    notes = TextAreaField('合作备注', validators=[Optional(), Length(max=500)], default='')

    def __init__(self, *args, **kwargs):
        # 检查是否为创建模式（通过检查是否传入了edit_mode参数）
        self.edit_mode = kwargs.pop('edit_mode', False)
        super(SupplierForm, self).__init__(*args, **kwargs)

        # 在创建模式下，学校和开始日期是必填的
        if not self.edit_mode:
            self.area_id.validators = [DataRequired(message='请选择合作学校')]
            self.start_date.validators = [DataRequired(message='请选择合作开始日期')]

    submit = SubmitField('提交')

class SupplierCategoryForm(FlaskForm):
    """供应商分类表单"""
    name = StringField('分类名称', validators=[DataRequired(message='请输入分类名称'), Length(min=2, max=50)])
    description = TextAreaField('描述', validators=[Optional(), Length(max=500)], default='')
    submit = SubmitField('提交')

class SupplierCertificateForm(FlaskForm):
    """供应商证书表单"""
    supplier_id = SelectField('供应商', coerce=int, validators=[DataRequired(message='请选择供应商')])
    certificate_type = StringField('证书类型', validators=[DataRequired(message='请输入证书类型'), Length(min=2, max=50)], default='营业执照')
    certificate_number = StringField('证书编号', validators=[DataRequired(message='请输入证书编号'), Length(min=2, max=100)])
    issue_date = DateField('发证日期', validators=[DataRequired(message='请选择发证日期')], default=date.today)
    expiry_date = DateField('过期日期', validators=[DataRequired(message='请选择过期日期')], default=lambda: date.today().replace(year=date.today().year + 1))
    issuing_authority = StringField('发证机构', validators=[DataRequired(message='请输入发证机构'), Length(min=2, max=100)], default='工商行政管理局')
    certificate_image = FileField('证书图片', validators=[Optional(), FileAllowed(['jpg', 'png', 'jpeg', 'pdf'], '只允许上传图片或PDF文件')])
    submit = SubmitField('提交')

class SupplierProductForm(FlaskForm):
    """供应商产品表单"""
    supplier_id = SelectField('供应商', coerce=int, validators=[DataRequired(message='请选择供应商')])
    ingredient_id = SelectField('食材', coerce=int, validators=[DataRequired(message='请选择食材')])
    product_code = StringField('产品编码', validators=[Optional(), Length(max=50)], default='')
    product_name = StringField('产品名称', validators=[Optional(), Length(max=100)], default='')
    model_number = StringField('型号', validators=[Optional(), Length(max=50)], default='')
    specification = StringField('规格', validators=[Optional(), Length(max=100)], default='')
    price = FloatField('单价', validators=[DataRequired(message='请输入单价'), NumberRange(min=0)], default=0.0)
    quality_cert = StringField('质量认证', validators=[DataRequired(message='请输入质量认证'), Length(min=2, max=200)], default='国家标准')
    quality_standard = StringField('质量标准', validators=[Optional(), Length(max=200)], default='GB/T')
    product_image = FileField('产品图片', validators=[Optional(), FileAllowed(['jpg', 'png', 'jpeg'], '只允许上传图片')])
    lead_time = IntegerField('供货周期(天)', validators=[Optional(), NumberRange(min=1)], default=3)
    min_order_quantity = FloatField('最小订购量', validators=[Optional(), NumberRange(min=0)], default=1.0)
    description = TextAreaField('产品描述', validators=[Optional(), Length(max=1000)], default='')
    submit = SubmitField('提交')

class ProductSpecParameterForm(FlaskForm):
    """产品规格参数表单"""
    product_id = HiddenField('产品ID', validators=[DataRequired()])
    param_name = StringField('参数名称', validators=[DataRequired(message='请输入参数名称'), Length(max=50)], default='规格')
    param_value = StringField('参数值', validators=[DataRequired(message='请输入参数值'), Length(max=100)], default='标准')
    param_unit = StringField('参数单位', validators=[Optional(), Length(max=20)], default='')
    submit = SubmitField('添加参数')

class SupplierSchoolRelationForm(FlaskForm):
    """供应商-学校关联表单"""
    supplier_id = SelectField('供应商', coerce=int, validators=[DataRequired(message='请选择供应商')])
    area_id = SelectField('学校', coerce=int, validators=[DataRequired(message='请选择学校')])
    contract_number = StringField('合同编号', validators=[Optional(), Length(max=100)], default='')
    start_date = DateField('合作开始日期', validators=[DataRequired(message='请选择合作开始日期')], default=date.today)
    end_date = DateField('合作结束日期', validators=[Optional()])
    status = SelectField('状态', choices=[(1, '有效'), (0, '已终止')], coerce=int, default=1)
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500)], default='')
    submit = SubmitField('提交')

class DeliveryInspectionForm(FlaskForm):
    """送货验收表单"""
    delivery_id = HiddenField('送货单ID', validators=[DataRequired()])
    inspection_result = SelectField('验收结果', choices=[
        ('全部通过', '全部通过'),
        ('部分通过', '部分通过'),
        ('全部拒收', '全部拒收')
    ], validators=[DataRequired(message='请选择验收结果')], default='全部通过')
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500)], default='')
    submit = SubmitField('提交验收结果')

class DeliveryItemInspectionForm(FlaskForm):
    """送货项验收表单"""
    inspection_id = HiddenField('验收ID', validators=[DataRequired()])
    delivery_item_id = HiddenField('送货项ID', validators=[DataRequired()])
    accepted_quantity = FloatField('验收合格数量', validators=[DataRequired(message='请输入验收合格数量'), NumberRange(min=0)], default=0.0)
    rejected_quantity = FloatField('拒收数量', validators=[DataRequired(message='请输入拒收数量'), NumberRange(min=0)], default=0.0)
    rejection_reason = StringField('拒收原因', validators=[Optional(), Length(max=200)], default='')
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500)], default='')
    submit = SubmitField('提交')
