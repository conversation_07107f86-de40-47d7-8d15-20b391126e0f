from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Ingredient, IngredientCategory, SupplierProduct
from werkzeug.utils import secure_filename
import os
import json
from datetime import datetime

ingredient_bp = Blueprint('ingredient', __name__)

def get_main_categories():
    """获取6大主要分类"""
    main_categories = ['肉类', '蔬菜类', '水果类', '谷物类', '调味品', '水产品']
    return IngredientCategory.query.filter(IngredientCategory.name.in_(main_categories)).all()

@ingredient_bp.route('/')
@login_required
def index():
    """食材列表页面 - 实现学校级数据隔离"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')
    view_mode = request.args.get('view_mode', 'list')  # 新增：显示模式 list/category

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食材和全局食材
    query = Ingredient.query.filter(
        db.or_(
            Ingredient.area_id.in_(area_ids),  # 用户学校的食材
            Ingredient.is_global == True,      # 全局食材（系统预设）
            Ingredient.area_id.is_(None)       # 兼容旧数据（无area_id的食材）
        )
    )

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Ingredient.name.like(f'%{keyword}%'))

    # 根据显示模式处理数据
    if view_mode == 'category':
        # 按分类分组显示 - 只显示6大分类
        main_categories = ['肉类', '蔬菜类', '水果类', '谷物类', '调味品', '水产品']
        ingredients_by_category = {}
        all_ingredients = query.order_by(Ingredient.category, Ingredient.name).all()

        for ingredient in all_ingredients:
            category_name = ingredient.category or '未分类'
            # 只显示6大分类
            if category_name in main_categories:
                if category_name not in ingredients_by_category:
                    ingredients_by_category[category_name] = []
                ingredients_by_category[category_name].append(ingredient)

        # 按指定顺序排列分类
        ordered_ingredients_by_category = {}
        for category in main_categories:
            if category in ingredients_by_category:
                ordered_ingredients_by_category[category] = ingredients_by_category[category]

        # 只获取6大分类
        categories = get_main_categories()

        return render_template('ingredient/index_category.html',
                              ingredients_by_category=ordered_ingredients_by_category,
                              categories=categories,
                              category_id=category_id,
                              keyword=keyword,
                              view_mode=view_mode)
    else:
        # 传统列表显示
        pagination = query.order_by(Ingredient.id.desc()).paginate(page=page, per_page=per_page)
        ingredients = pagination.items

        # 只获取6大分类
        categories = get_main_categories()

        return render_template('ingredient/index.html',
                              ingredients=ingredients,
                              pagination=pagination,
                              categories=categories,
                              category_id=category_id,
                              keyword=keyword,
                              view_mode=view_mode)

@ingredient_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建食材"""
    if request.method == 'POST':
        name = request.form.get('name')
        category_id = request.form.get('category_id') or None
        unit = request.form.get('unit')
        specification = request.form.get('specification')
        storage_temp = request.form.get('storage_temp')
        storage_condition = request.form.get('storage_condition')
        shelf_life = request.form.get('shelf_life')
        nutrition_info = request.form.get('nutrition_info')

        # 处理图片上传
        base_image = None
        if 'base_image' in request.files and request.files['base_image'].filename:
            file = request.files['base_image']
            filename = secure_filename(file.filename)
            # 确保文件名唯一
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
            upload_folder = os.path.join(current_app.static_folder, 'uploads/ingredients')

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=1)

            file_path = os.path.join(upload_folder, unique_filename)
            file.save(file_path)

            # 存储相对路径
            base_image = f"uploads/ingredients/{unique_filename}"

        # 获取当前用户的主要学校区域
        user_area = current_user.get_primary_area()
        if not user_area:
            flash('无法确定您的学校信息，请联系管理员', 'error')
            return redirect(url_for('ingredient.index'))

        ingredient = Ingredient(
            name=name,
            category_id=category_id,
            area_id=user_area.id,  # 绑定到用户的学校
            unit=unit,
            specification=specification,
            storage_temp=storage_temp,
            storage_condition=storage_condition,
            shelf_life=shelf_life,
            nutrition_info=nutrition_info,
            base_image=base_image,
            is_global=False  # 用户创建的食材不是全局食材
        )

        db.session.add(ingredient)

        # 添加审计日志
        from app.utils.log_activity import log_activity
        log_activity(
            action='create',
            resource_type='Ingredient',
            details=ingredient.to_dict()
        )

        db.session.commit()
        flash('食材创建成功！', 'success')
        return redirect(url_for('ingredient.index'))

    # GET 请求，显示创建表单 - 只显示6大分类
    categories = get_main_categories()
    return render_template('ingredient/form.html', ingredient=None, categories=categories)

@ingredient_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑食材 - 权限控制：超级管理员可以编辑所有食材，普通用户只能编辑学校食材"""
    ingredient = Ingredient.query.get_or_404(id)

    # 权限检查：超级管理员可以编辑所有食材，普通用户只能编辑学校食材
    user_area = current_user.get_current_area()
    if not current_user.is_admin():
        if ingredient.is_global:
            flash('系统食材不能直接编辑，请联系管理员', 'warning')
            return redirect(url_for('ingredient.view', id=ingredient.id))
        if ingredient.area_id and ingredient.area_id != user_area.id:
            flash('您没有权限编辑此食材', 'danger')
            return redirect(url_for('ingredient.index'))

    if request.method == 'POST':
        name = request.form.get('name')
        category_id = request.form.get('category_id') or None
        unit = request.form.get('unit')
        specification = request.form.get('specification')
        storage_temp = request.form.get('storage_temp')
        storage_condition = request.form.get('storage_condition')
        shelf_life = request.form.get('shelf_life')
        nutrition_info = request.form.get('nutrition_info')

        old_data = ingredient.to_dict()

        # 处理图片上传
        if 'base_image' in request.files and request.files['base_image'].filename:
            file = request.files['base_image']
            filename = secure_filename(file.filename)
            # 确保文件名唯一
            unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"
            upload_folder = os.path.join(current_app.static_folder, 'uploads/ingredients')

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=1)

            file_path = os.path.join(upload_folder, unique_filename)
            file.save(file_path)

            # 删除旧图片
            if ingredient.base_image:
                old_file_path = os.path.join(current_app.static_folder, ingredient.base_image)
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)

            # 存储相对路径
            ingredient.base_image = f"uploads/ingredients/{unique_filename}"

        ingredient.name = name
        ingredient.category_id = category_id
        ingredient.unit = unit
        ingredient.specification = specification
        ingredient.storage_temp = storage_temp
        ingredient.storage_condition = storage_condition
        ingredient.shelf_life = shelf_life
        ingredient.nutrition_info = nutrition_info

        # 添加审计日志
        from app.utils.log_activity import log_activity
        log_activity(
            action='update',
            resource_type='Ingredient',
            resource_id=ingredient.id,
            details={
                'old': old_data,
                'new': ingredient.to_dict()
            }
        )

        db.session.commit()
        flash('食材更新成功！', 'success')
        return redirect(url_for('ingredient.index'))

    # GET 请求，显示编辑表单 - 只显示6大分类
    categories = get_main_categories()
    return render_template('ingredient/form.html', ingredient=ingredient, categories=categories)

@ingredient_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除食材 - 权限控制：超级管理员可以删除所有食材，普通用户只能删除学校食材"""
    ingredient = Ingredient.query.get_or_404(id)

    # 权限检查：超级管理员可以删除所有食材，普通用户只能删除自己学校的食材
    user_area = current_user.get_current_area()
    if not current_user.is_admin():
        if ingredient.is_global:
            return jsonify({
                'success': 0,
                'message': '系统食材不能删除，请联系管理员'
            })
        if ingredient.area_id and ingredient.area_id != user_area.id:
            return jsonify({
                'success': 0,
                'message': '您没有权限删除此食材'
            })

    # 检查是否有关联的供应商产品
    if SupplierProduct.query.filter_by(ingredient_id=id).first():
        return jsonify({'success': 0, 'message': '该食材已关联供应商产品，不能删除！'})

    # 检查是否有关联的食谱
    if ingredient.recipe_ingredients.count() > 0:
        return jsonify({'success': 0, 'message': '该食材已关联食谱，不能删除！'})

    # 添加审计日志
    from app.utils.log_activity import log_activity
    log_activity(
        action='delete',
        resource_type='Ingredient',
        resource_id=ingredient.id,
        details=ingredient.to_dict()
    )

    # 删除图片
    if ingredient.base_image:
        file_path = os.path.join(current_app.static_folder, ingredient.base_image)
        if os.path.exists(file_path):
            os.remove(file_path)

    db.session.delete(ingredient)
    db.session.commit()

    return jsonify({'success': 1, 'message': '食材删除成功！'})

@ingredient_bp.route('/<int:id>/view')
@login_required
def view(id):
    """查看食材详情"""
    ingredient = Ingredient.query.get_or_404(id)

    # 添加审计日志
    from app.utils.log_activity import log_activity
    log_activity(
        action='view',
        resource_type='Ingredient',
        resource_id=ingredient.id
    )

    return render_template('ingredient/view.html', ingredient=ingredient)

@ingredient_bp.route('/api')
@login_required
def api_list():
    """食材API - 实现学校级数据隔离"""
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 构建查询 - 只显示用户学校的食材和全局食材
    query = Ingredient.query.filter(
        db.or_(
            Ingredient.area_id.in_(area_ids),  # 用户学校的食材
            Ingredient.is_global == True,      # 全局食材（系统预设）
            Ingredient.area_id.is_(None)       # 兼容旧数据（无area_id的食材）
        )
    )

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Ingredient.name.like(f'%{keyword}%'))

    ingredients = query.all()
    return jsonify([ingredient.to_dict() for ingredient in ingredients])

def get_ingredient_traceability(ingredient_id, area_ids):
    """获取食材溯源信息 - 通过批次号关联到消耗计划和食谱"""
    from app.models import StockOutItem, StockOut, ConsumptionPlan, MenuPlan, MenuRecipe, Recipe, RecipeIngredient
    from sqlalchemy import text

    try:
        # 通过批次号查找该食材的消耗轨迹，包含供应商信息
        traceability_query = text("""
            SELECT DISTINCT
                soi.batch_number,
                soi.quantity as consumed_quantity,
                soi.unit,
                so.stock_out_date,
                cp.consumption_date,
                cp.meal_type,
                cp.diners_count,
                mp.plan_date,
                mr.recipe_id,
                r.name as recipe_name,
                r.category as recipe_category,
                aa.name as area_name,
                s.name as supplier_name,
                s.contact_person as supplier_contact,
                inv.production_date,
                inv.expiry_date
            FROM stock_out_items soi
            JOIN stock_outs so ON soi.stock_out_id = so.id
            JOIN warehouses w ON so.warehouse_id = w.id
            LEFT JOIN consumption_plans cp ON so.consumption_plan_id = cp.id
            LEFT JOIN menu_plans mp ON cp.menu_plan_id = mp.id
            LEFT JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
            LEFT JOIN recipes r ON mr.recipe_id = r.id
            LEFT JOIN recipe_ingredients ri ON r.id = ri.recipe_id AND ri.ingredient_id = :ingredient_id
            LEFT JOIN administrative_areas aa ON w.area_id = aa.id
            LEFT JOIN inventories inv ON soi.batch_number = inv.batch_number AND inv.ingredient_id = :ingredient_id
            LEFT JOIN suppliers s ON inv.supplier_id = s.id
            WHERE soi.ingredient_id = :ingredient_id
            AND w.area_id IN ({})
            AND so.status = '已出库'
            AND ri.ingredient_id IS NOT NULL  -- 只显示确实使用了该食材的食谱
            ORDER BY so.stock_out_date DESC, cp.consumption_date DESC
            LIMIT 50
        """.format(','.join(map(str, area_ids))))

        result = db.session.execute(traceability_query, {
            'ingredient_id': ingredient_id
        }).fetchall()

        # 组织数据结构
        traceability_data = []
        for row in result:
            traceability_data.append({
                'batch_number': row.batch_number,
                'consumed_quantity': float(row.consumed_quantity) if row.consumed_quantity else 0,
                'unit': row.unit,
                'stock_out_date': row.stock_out_date,
                'consumption_date': row.consumption_date,
                'meal_type': row.meal_type,
                'diners_count': row.diners_count,
                'recipe_name': row.recipe_name,
                'recipe_category': row.recipe_category,
                'area_name': row.area_name,
                'supplier_name': row.supplier_name,
                'supplier_contact': row.supplier_contact,
                'production_date': row.production_date,
                'expiry_date': row.expiry_date
            })

        return traceability_data

    except Exception as e:
        current_app.logger.error(f"获取食材溯源信息时出错: {str(e)}")
        return []

@ingredient_bp.route('/turnover/<int:id>')
@login_required
def turnover(id):
    """食材周转情况页面"""
    from app.models import Inventory, StockInItem, StockOutItem, StockIn, StockOut, Warehouse
    from sqlalchemy import func, desc

    ingredient = Ingredient.query.get_or_404(id)

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取该食材的库存记录 - 只显示有库存的记录
    inventories = Inventory.query.join(Warehouse).filter(
        Inventory.ingredient_id == id,
        Warehouse.area_id.in_(area_ids),
        Inventory.status == '正常',
        func.cast(Inventory.quantity, db.Numeric) > 0  # 只显示数量大于0的库存
    ).order_by(desc(Inventory.created_at)).all()

    # 获取该食材的入库记录
    stock_in_items = StockInItem.query.join(StockIn).join(Warehouse).filter(
        StockInItem.ingredient_id == id,
        Warehouse.area_id.in_(area_ids)
    ).order_by(desc(StockIn.stock_in_date)).limit(20).all()

    # 获取该食材的出库记录
    stock_out_items = StockOutItem.query.join(StockOut).join(Warehouse).filter(
        StockOutItem.ingredient_id == id,
        Warehouse.area_id.in_(area_ids)
    ).order_by(desc(StockOut.stock_out_date)).limit(20).all()

    # 获取该食材的溯源信息 - 通过批次号关联到消耗计划和食谱
    traceability_info = get_ingredient_traceability(id, area_ids)

    # 计算汇总信息 - 使用 CAST 转换字符串为数值
    total_stock_in = db.session.query(func.sum(func.cast(StockInItem.quantity, db.Numeric))).join(StockIn).join(Warehouse).filter(
        StockInItem.ingredient_id == id,
        Warehouse.area_id.in_(area_ids),
        StockIn.status == '已入库'
    ).scalar() or 0

    total_stock_out = db.session.query(func.sum(func.cast(StockOutItem.quantity, db.Numeric))).join(StockOut).join(Warehouse).filter(
        StockOutItem.ingredient_id == id,
        Warehouse.area_id.in_(area_ids),
        StockOut.status == '已出库'
    ).scalar() or 0

    current_stock = db.session.query(func.sum(func.cast(Inventory.quantity, db.Numeric))).join(Warehouse).filter(
        Inventory.ingredient_id == id,
        Warehouse.area_id.in_(area_ids),
        Inventory.status == '正常'
    ).scalar() or 0

    turnover_info = {
        'total_stock_in': float(total_stock_in),
        'total_stock_out': float(total_stock_out),
        'current_stock': float(current_stock),
        'turnover_rate': round((float(total_stock_out) / float(total_stock_in)) * 100, 1) if total_stock_in > 0 else 0
    }

    return render_template('ingredient/turnover.html',
                          ingredient=ingredient,
                          inventories=inventories,
                          stock_in_items=stock_in_items,
                          stock_out_items=stock_out_items,
                          turnover_info=turnover_info,
                          traceability_info=traceability_info)
