"""
系统会计科目管理路由
只有超级管理员可以管理系统会计科目
"""

from flask import render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountingSubject
from app.forms.financial import AccountingSubjectForm
from app.utils.decorators import admin_required
from sqlalchemy import text


@financial_bp.route('/system-accounting-subjects')
@login_required
@admin_required
def system_accounting_subjects_index():
    """系统会计科目列表（仅超级管理员）"""
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    subject_type = request.args.get('subject_type', '').strip()
    
    # 构建查询：只查询系统科目
    query = AccountingSubject.query.filter_by(area_id=1, is_system=True)
    
    if keyword:
        query = query.filter(
            db.or_(
                AccountingSubject.code.like(f'%{keyword}%'),
                AccountingSubject.name.like(f'%{keyword}%')
            )
        )
    
    if subject_type:
        query = query.filter_by(subject_type=subject_type)
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    subjects = query.order_by(AccountingSubject.code).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('financial/system_accounting_subjects/index.html',
                         subjects=subjects,
                         keyword=keyword,
                         subject_type=subject_type)


@financial_bp.route('/system-accounting-subjects/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_system_accounting_subject():
    """创建系统会计科目（仅超级管理员）"""
    form = AccountingSubjectForm()
    
    # 修改表单的上级科目选择，只显示系统科目
    system_subjects = AccountingSubject.query.filter_by(
        area_id=1, 
        is_system=True, 
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    choices = [(0, '无上级科目')]
    for subject in system_subjects:
        choices.append((subject.id, f"{subject.code} - {subject.name}"))
    form.parent_id.choices = choices
    
    if form.validate_on_submit():
        try:
            # 检查科目编码是否已存在（在系统科目中）
            existing = AccountingSubject.query.filter_by(
                area_id=1,
                is_system=True,
                code=form.code.data
            ).first()
            
            if existing:
                flash('系统科目编码已存在，请使用其他编码', 'danger')
                return render_template('financial/system_accounting_subjects/form.html', form=form)
            
            # 使用原始SQL创建记录
            sql = text("""
                INSERT INTO accounting_subjects 
                (code, name, parent_id, level, subject_type, balance_direction, 
                 area_id, is_system, is_active, description, created_by)
                OUTPUT inserted.id
                VALUES 
                (:code, :name, :parent_id, :level, :subject_type, :balance_direction,
                 :area_id, :is_system, :is_active, :description, :created_by)
            """)
            
            # 计算科目级别
            level = 1
            if form.parent_id.data and form.parent_id.data > 0:
                parent = AccountingSubject.query.get(form.parent_id.data)
                if parent:
                    level = parent.level + 1
            
            params = {
                'code': form.code.data,
                'name': form.name.data,
                'parent_id': form.parent_id.data if form.parent_id.data > 0 else None,
                'level': level,
                'subject_type': form.subject_type.data,
                'balance_direction': form.balance_direction.data,
                'area_id': 1,  # 系统科目固定使用area_id=1
                'is_system': True,  # 标记为系统科目
                'is_active': True,
                'description': form.description.data,
                'created_by': current_user.id
            }
            
            result = db.session.execute(sql, params)
            subject_id = result.fetchone()[0]
            db.session.commit()
            
            flash('系统会计科目创建成功', 'success')
            return redirect(url_for('financial.system_accounting_subjects_index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建系统会计科目失败: {str(e)}")
            flash('创建失败，请重试', 'danger')
    
    return render_template('financial/system_accounting_subjects/form.html', form=form)


@financial_bp.route('/system-accounting-subjects/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_system_accounting_subject(id):
    """编辑系统会计科目（仅超级管理员）"""
    subject = AccountingSubject.query.filter_by(
        id=id, 
        area_id=1,
        is_system=True
    ).first_or_404()
    
    form = AccountingSubjectForm(obj=subject)
    
    # 修改表单的上级科目选择，只显示系统科目
    system_subjects = AccountingSubject.query.filter_by(
        area_id=1, 
        is_system=True, 
        is_active=True
    ).order_by(AccountingSubject.code).all()
    
    choices = [(0, '无上级科目')]
    for subj in system_subjects:
        if subj.id != id:  # 排除自己
            choices.append((subj.id, f"{subj.code} - {subj.name}"))
    form.parent_id.choices = choices
    
    if form.validate_on_submit():
        try:
            # 检查科目编码是否已存在（排除当前记录）
            existing = AccountingSubject.query.filter(
                AccountingSubject.area_id == 1,
                AccountingSubject.is_system == True,
                AccountingSubject.code == form.code.data,
                AccountingSubject.id != id
            ).first()
            
            if existing:
                flash('系统科目编码已存在，请使用其他编码', 'danger')
                return render_template('financial/system_accounting_subjects/form.html', 
                                     form=form, subject=subject)
            
            # 使用原始SQL更新记录
            sql = text("""
                UPDATE accounting_subjects
                SET code = :code,
                    name = :name,
                    parent_id = :parent_id,
                    level = :level,
                    subject_type = :subject_type,
                    balance_direction = :balance_direction,
                    description = :description
                WHERE id = :id AND area_id = 1 AND is_system = 1
            """)
            
            # 计算科目级别
            level = 1
            if form.parent_id.data and form.parent_id.data > 0:
                parent = AccountingSubject.query.get(form.parent_id.data)
                if parent:
                    level = parent.level + 1
            
            params = {
                'code': form.code.data,
                'name': form.name.data,
                'parent_id': form.parent_id.data if form.parent_id.data > 0 else None,
                'level': level,
                'subject_type': form.subject_type.data,
                'balance_direction': form.balance_direction.data,
                'description': form.description.data,
                'id': id
            }
            
            db.session.execute(sql, params)
            db.session.commit()
            
            flash('系统会计科目更新成功', 'success')
            return redirect(url_for('financial.system_accounting_subjects_index'))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新系统会计科目失败: {str(e)}")
            flash('更新失败，请重试', 'danger')
    
    return render_template('financial/system_accounting_subjects/form.html', 
                         form=form, subject=subject)


@financial_bp.route('/system-accounting-subjects/<int:id>/delete', methods=['POST'])
@login_required
@admin_required
def delete_system_accounting_subject(id):
    """删除系统会计科目（仅超级管理员）"""
    subject = AccountingSubject.query.filter_by(
        id=id, 
        area_id=1,
        is_system=True
    ).first_or_404()
    
    # 检查是否有下级科目
    children = AccountingSubject.query.filter_by(parent_id=id).first()
    if children:
        flash('该系统科目有下级科目，不能删除', 'warning')
        return redirect(url_for('financial.system_accounting_subjects_index'))
    
    # 检查是否被学校科目引用
    school_subjects = AccountingSubject.query.filter_by(parent_id=id, is_system=False).first()
    if school_subjects:
        flash('该系统科目被学校科目引用，不能删除', 'warning')
        return redirect(url_for('financial.system_accounting_subjects_index'))
    
    try:
        # 使用原始SQL删除
        sql = text("DELETE FROM accounting_subjects WHERE id = :id AND area_id = 1 AND is_system = 1")
        db.session.execute(sql, {'id': id})
        db.session.commit()
        
        flash('系统会计科目删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除系统会计科目失败: {str(e)}")
        flash('删除失败，该科目可能已被使用', 'danger')
    
    return redirect(url_for('financial.system_accounting_subjects_index'))
