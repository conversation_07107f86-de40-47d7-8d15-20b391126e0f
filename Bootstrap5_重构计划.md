# Bootstrap 5.3.6 全面重构计划

## 项目概述

本项目旨在将现有的校园餐智慧食堂平台从混合的Bootstrap版本全面升级到Bootstrap 5.3.6，并采用统一的左右结构布局，提升用户体验和界面一致性。

## 重构目标

1. **统一使用Bootstrap 5.3.6**：移除所有旧版本Bootstrap依赖
2. **采用左右结构布局**：侧边栏+主内容区的现代化布局
3. **保持现有功能完整性**：确保所有业务功能正常运行
4. **提升用户体验**：改善界面交互和视觉效果
5. **确保响应式设计**：完美适配各种设备屏幕

## 已完成的工作

### 1. 基础架构重构

#### 核心模板文件
- ✅ `app/templates/base.html` - 主基础模板，Bootstrap 5.3.6 + 左右布局
- ✅ `app/templates/base_sidebar.html` - 专用侧边栏布局模板
- ✅ `app/templates/auth/login.html` - 登录页面重构示例

#### CSS样式文件
- ✅ `app/static/css/bootstrap5-theme.css` - Bootstrap 5.3.6 主题样式
- ✅ `app/static/css/layout-sidebar.css` - 左右结构布局样式
- ✅ `app/static/css/components-bs5.css` - Bootstrap 5组件增强样式
- ✅ `app/static/css/mobile-responsive.css` - 移动端响应式样式

#### JavaScript功能文件
- ✅ `app/static/js/bootstrap5-theme.js` - 主题管理器
- ✅ `app/static/js/sidebar-bs5.js` - 侧边栏管理器
- ✅ `app/static/js/global-bs5.js` - 全局功能管理器

### 2. 示例页面重构

#### 已重构页面
- ✅ `app/templates/daily_management/index.html` - 食堂日常管理首页
- ✅ `app/templates/main/index.html` - 主页（Landing页面）
- ✅ `app/templates/main/dashboard.html` - 控制台页面
- ✅ `app/templates/financial/base.html` - 财务模块基础模板
- ✅ `app/templates/financial/` - 财务管理模块（所有页面）
- ✅ `app/templates/base_landing.html` - Landing页面基础模板

## 重构特性

### 1. Bootstrap 5.3.6 新特性
- 使用最新的CSS变量系统
- 改进的响应式断点
- 增强的组件和工具类
- 更好的无障碍支持
- 优化的性能和文件大小

### 2. 左右结构布局
- 固定顶部导航栏
- 可折叠侧边栏
- 响应式主内容区
- 移动端适配

### 3. 主题系统
- 多主题支持（海洋蓝、自然绿、温暖橙等）
- 暗色模式支持
- 主题本地存储
- 键盘快捷键支持

### 4. 组件增强
- 统一的卡片样式
- 改进的表格组件
- 增强的表单验证
- 优化的按钮和导航

### 5. 移动端优化
- 响应式设计
- 触摸友好的界面
- 移动端专用组件
- 性能优化

## 待重构的模板文件

### 🔥 高优先级（核心业务模块）
- ✅ `app/templates/main/index.html` - 首页（已重构为Bootstrap 5.3.6）
- ✅ `app/templates/main/dashboard.html` - 控制台（已重构为Bootstrap 5.3.6）
- ✅ `app/templates/financial/` - 财务管理模块（已适配Bootstrap 5.3.6）
- [ ] `app/templates/stock_in/` - 入库管理模块（高频使用）
- [ ] `app/templates/stock_out/` - 出库管理模块（高频使用）
- [ ] `app/templates/inventory/` - 库存管理模块（核心功能）

### 🔶 中优先级（重要功能模块）
- [ ] `app/templates/supplier/` - 供应商管理模块
- [ ] `app/templates/recipe/` - 菜谱管理模块
- [ ] `app/templates/ingredient/` - 食材管理模块
- [ ] `app/templates/weekly_menu/` - 周菜单管理模块
- [ ] `app/templates/purchase_order/` - 采购订单模块
- [ ] `app/templates/consumption_plan/` - 消费计划模块

### 🔷 中低优先级（管理功能）
- [ ] `app/templates/admin/` - 系统管理
- [ ] `app/templates/employee/` - 员工管理
- [ ] `app/templates/area/` - 区域管理
- [ ] `app/templates/warehouse/` - 仓库管理
- [ ] `app/templates/daily_management/` - 日常管理（部分已重构）

### 🔹 低优先级（辅助功能）
- [ ] `app/templates/auth/register.html` - 注册页面
- [ ] `app/templates/auth/reset_password.html` - 密码重置
- [ ] `app/templates/auth/change_password.html` - 密码修改
- [ ] `app/templates/food_sample/` - 食品留样
- [ ] `app/templates/inspection/` - 检查管理
- [ ] `app/templates/notification/` - 通知管理

## 重构步骤

### 第一阶段：核心模板（已完成）
1. ✅ 重构基础模板
2. ✅ 创建CSS和JS文件
3. ✅ 实现主题系统
4. ✅ 完成示例页面

### 第二阶段：主要功能模块
1. 重构认证相关页面
2. 重构主页和控制台
3. 重构业务功能模块
4. 测试功能完整性

### 第三阶段：系统管理模块
1. 重构管理后台页面
2. 重构用户管理页面
3. 重构系统设置页面
4. 全面测试

### 第四阶段：优化和完善
1. 性能优化
2. 无障碍支持
3. 浏览器兼容性测试
4. 用户体验优化

## 技术规范

### HTML结构
```html
{% extends 'base_sidebar.html' %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">首页</a></li>
        <li class="breadcrumb-item active">当前页面</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <h1 class="page-title">页面标题</h1>
        <p class="page-subtitle">页面描述</p>
    </div>
    
    <!-- 页面内容 -->
    <div class="row g-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">卡片标题</h5>
                </div>
                <div class="card-body">
                    <!-- 卡片内容 -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
```

### CSS类命名规范
- 使用Bootstrap 5.3.6的工具类
- 自定义类使用BEM命名法
- 响应式断点：xs, sm, md, lg, xl, xxl

### JavaScript规范
- 使用ES6+语法
- 模块化设计
- 事件委托
- 性能优化

## 测试计划

### 功能测试
- [ ] 所有页面正常加载
- [ ] 表单提交功能正常
- [ ] 数据展示正确
- [ ] 用户权限控制

### 兼容性测试
- [ ] Chrome 90+
- [ ] Firefox 88+
- [ ] Safari 14+
- [ ] Edge 90+

### 响应式测试
- [ ] 手机端 (320px-767px)
- [ ] 平板端 (768px-1023px)
- [ ] 桌面端 (1024px+)

### 性能测试
- [ ] 页面加载速度
- [ ] 资源优化
- [ ] 内存使用

## 注意事项

1. **向后兼容**：确保现有功能不受影响
2. **数据安全**：重构过程中保护用户数据
3. **渐进式升级**：分模块逐步重构
4. **充分测试**：每个模块重构后进行全面测试
5. **文档更新**：及时更新相关文档

## 预期收益

1. **用户体验提升**：现代化的界面设计
2. **维护成本降低**：统一的技术栈
3. **开发效率提高**：标准化的组件库
4. **性能优化**：更快的加载速度
5. **移动端体验**：更好的移动设备支持

## 时间计划

- **第一阶段**：已完成 ✅
- **第二阶段**：预计2-3周
- **第三阶段**：预计1-2周  
- **第四阶段**：预计1周

总预计完成时间：4-6周

---

*本文档将随着重构进度持续更新*
