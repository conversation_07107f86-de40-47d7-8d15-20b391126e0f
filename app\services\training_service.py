"""
培训记录服务

提供食堂培训记录管理所需的数据服务，包括培训记录的创建、获取、更新和删除等。
"""

from datetime import datetime
from app import db
from app.models_daily_management import CanteenTrainingRecord, Photo
from app.utils.log_activity import log_activity


class TrainingService:
    """培训记录服务类"""

    @staticmethod
    def get_trainings_by_daily_log(daily_log_id):
        """
        获取日志的培训记录
        
        Args:
            daily_log_id: 日志ID
            
        Returns:
            list: 培训记录列表
        """
        return CanteenTrainingRecord.query.filter_by(daily_log_id=daily_log_id).all()

    @staticmethod
    def get_training_by_id(training_id):
        """
        根据ID获取培训记录
        
        Args:
            training_id: 培训记录ID
            
        Returns:
            CanteenTrainingRecord: 培训记录对象
        """
        return CanteenTrainingRecord.query.get(training_id)

    @staticmethod
    def create_training(data):
        """
        创建培训记录
        
        Args:
            data: 培训记录数据
            
        Returns:
            CanteenTrainingRecord: 创建的培训记录对象
        """
        training = CanteenTrainingRecord(
            daily_log_id=data.get('daily_log_id'),
            training_topic=data.get('training_topic'),
            trainer=data.get('trainer'),
            training_time=data.get('training_time'),
            location=data.get('location'),
            duration=data.get('duration'),
            attendees_count=data.get('attendees_count'),
            content_summary=data.get('content_summary'),
            effectiveness_evaluation=data.get('effectiveness_evaluation')
        )
        
        db.session.add(training)
        db.session.commit()
        
        # 记录活动
        log_activity('创建了培训记录', f'主题: {training.training_topic}', 'training', training.id)
        
        return training

    @staticmethod
    def update_training(training_id, data):
        """
        更新培训记录
        
        Args:
            training_id: 培训记录ID
            data: 培训记录数据
            
        Returns:
            CanteenTrainingRecord: 更新后的培训记录对象
        """
        training = CanteenTrainingRecord.query.get(training_id)
        
        if not training:
            return None
            
        # 更新培训记录
        training.training_topic = data.get('training_topic', training.training_topic)
        training.trainer = data.get('trainer', training.trainer)
        training.training_time = data.get('training_time', training.training_time)
        training.location = data.get('location', training.location)
        training.duration = data.get('duration', training.duration)
        training.attendees_count = data.get('attendees_count', training.attendees_count)
        training.content_summary = data.get('content_summary', training.content_summary)
        training.effectiveness_evaluation = data.get('effectiveness_evaluation', training.effectiveness_evaluation)
        
        db.session.commit()
        
        # 记录活动
        log_activity('更新了培训记录', f'主题: {training.training_topic}', 'training', training.id)
        
        return training

    @staticmethod
    def delete_training(training_id):
        """
        删除培训记录
        
        Args:
            training_id: 培训记录ID
            
        Returns:
            bool: 是否删除成功
        """
        training = CanteenTrainingRecord.query.get(training_id)
        
        if not training:
            return False
            
        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='training', reference_id=training_id).all()
        for photo in photos:
            db.session.delete(photo)
            
        # 记录活动
        training_topic = training.training_topic
        
        # 删除培训记录
        db.session.delete(training)
        db.session.commit()
        
        # 记录活动
        log_activity('删除了培训记录', f'主题: {training_topic}', 'training', training_id)
        
        return True
