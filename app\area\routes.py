from flask import render_template, redirect, url_for, flash, request, session, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.area import area_bp
from app.area.forms import AreaForm, MoveAreaForm
from app.models import AdministrativeArea, AreaChangeHistory, AuditLog, User
from app.utils import admin_required, area_required
from app.utils.log_activity import log_activity
from app.utils.raw_sql_helper import insert_area_change_history
from sqlalchemy import text
import json
from datetime import datetime

@area_bp.route('/')
@login_required
def index():
    """区域管理首页"""
    # 获取用户当前区域
    current_area = current_user.get_current_area()

    # 如果用户不是管理员，直接显示其所在区域
    if not current_user.is_admin():
        if not current_area:
            flash('您没有关联任何区域，无法访问区域管理', 'danger')
            return redirect(url_for('main.index'))

        # 直接重定向到用户所在区域的详情页
        return redirect(url_for('area.view_area', id=current_area.id))

    # 管理员可以查看所有顶级区域
    top_level_areas = AdministrativeArea.query.filter_by(parent_id=None).order_by(AdministrativeArea.name).all()

    # 获取当前区域路径
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template(
        'area/index.html',
        title='区域管理',
        top_level_areas=top_level_areas,
        current_area=current_area,
        area_path=area_path,
        now=datetime.now()
    )

@area_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_area():
    """添加区域"""
    form = AreaForm()

    # 获取可选的父级区域
    parent_choices = [(0, '顶级区域')]
    areas = AdministrativeArea.query.order_by(AdministrativeArea.level, AdministrativeArea.name).all()
    for area in areas:
        parent_choices.append((area.id, f"{area.get_level_name()} - {area.name}"))

    form.parent_id.choices = parent_choices

    if form.validate_on_submit():
        # 创建区域 - 使用原始SQL避免日期时间精度问题
        sql = text('''
        INSERT INTO administrative_areas
        (name, code, level, parent_id, description, status, is_township_school, created_at)
        OUTPUT inserted.id
        VALUES
        (:name, :code, :level, :parent_id, :description, :status, :is_township_school, GETDATE())
        ''')

        result = db.session.execute(
            sql,
            {
                'name': form.name.data,
                'code': form.code.data,
                'level': form.level.data,
                'parent_id': form.parent_id.data if form.parent_id.data > 0 else None,
                'description': form.description.data,
                'is_township_school': form.is_township_school.data,
                'status': 1
            }
        )

        # 获取新插入的区域ID
        area_id = result.scalar()

        # 查询新创建的区域对象
        area = AdministrativeArea.query.get(area_id)

        # 记录变更历史 - 使用原始SQL避免日期时间精度问题
        sql = text('''
        INSERT INTO area_change_history
        (area_id, change_type, new_parent_id, new_data, changed_by, created_at)
        VALUES
        (:area_id, :change_type, :new_parent_id, :new_data, :changed_by, GETDATE())
        ''')

        db.session.execute(
            sql,
            {
                'area_id': area.id,
                'change_type': 'create',
                'new_parent_id': area.parent_id,
                'new_data': json.dumps(area.to_dict()),
                'changed_by': current_user.id
            }
        )

        # 记录审计日志 - 使用原始SQL避免日期时间精度问题
        audit_sql = text("""
        INSERT INTO audit_logs
        (user_id, action, resource_type, resource_id, area_id, details)
        VALUES
        (:user_id, :action, :resource_type, :resource_id, :area_id, :details)
        """)

        db.session.execute(
            audit_sql,
            {
                'user_id': current_user.id,
                'action': 'create',
                'resource_type': 'area',
                'resource_id': area.id,
                'area_id': area.id,
                'details': json.dumps({
                    'area': area.to_dict()
                })
            }
        )
        db.session.commit()

        flash(f'区域 {area.name} 添加成功', 'success')
        return redirect(url_for('area.index'))

    return render_template(
        'area/area_form.html',
        title='添加区域',
        form=form,
        now=datetime.now()
    )

@area_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_area(id):
    """编辑区域"""
    area = AdministrativeArea.query.get_or_404(id)
    form = AreaForm(obj=area)
    form.area_id = id

    # 获取可选的父级区域（排除自己及其子区域）
    parent_choices = [(0, '顶级区域')]
    excluded_ids = [area.id] + [a.id for a in area.get_descendants()]
    areas = AdministrativeArea.query.filter(~AdministrativeArea.id.in_(excluded_ids)).order_by(AdministrativeArea.level, AdministrativeArea.name).all()

    for a in areas:
        parent_choices.append((a.id, f"{a.get_level_name()} - {a.name}"))

    form.parent_id.choices = parent_choices

    if request.method == 'GET':
        form.parent_id.data = area.parent_id if area.parent_id else 0

    if form.validate_on_submit():
        # 保存旧数据用于记录历史
        old_data = area.to_dict()
        old_parent_id = area.parent_id

        # 更新区域
        area.name = form.name.data
        area.code = form.code.data
        area.level = form.level.data
        area.parent_id = form.parent_id.data if form.parent_id.data > 0 else None
        area.description = form.description.data
        area.is_township_school = form.is_township_school.data

        db.session.commit()

        # 记录变更历史 - 使用原始SQL避免日期时间精度问题
        sql = text('''
        INSERT INTO area_change_history
        (area_id, change_type, old_parent_id, new_parent_id, old_data, new_data, changed_by, created_at)
        VALUES
        (:area_id, :change_type, :old_parent_id, :new_parent_id, :old_data, :new_data, :changed_by, GETDATE())
        ''')

        db.session.execute(
            sql,
            {
                'area_id': area.id,
                'change_type': 'update',
                'old_parent_id': old_parent_id,
                'new_parent_id': area.parent_id,
                'old_data': json.dumps(old_data),
                'new_data': json.dumps(area.to_dict()),
                'changed_by': current_user.id
            }
        )

        # 记录审计日志 - 使用原始SQL避免日期时间精度问题
        audit_sql = text("""
        INSERT INTO audit_logs
        (user_id, action, resource_type, resource_id, area_id, details)
        VALUES
        (:user_id, :action, :resource_type, :resource_id, :area_id, :details)
        """)

        db.session.execute(
            audit_sql,
            {
                'user_id': current_user.id,
                'action': 'update',
                'resource_type': 'area',
                'resource_id': area.id,
                'area_id': area.id,
                'details': json.dumps({
                    'old': old_data,
                    'new': area.to_dict()
                })
            }
        )
        db.session.commit()

        flash(f'区域 {area.name} 更新成功', 'success')
        return redirect(url_for('area.index'))

    return render_template(
        'area/area_form.html',
        title=f'编辑区域 - {area.name}',
        form=form,
        area=area,
        now=datetime.now()
    )

@area_bp.route('/view/<int:id>')
@login_required
def view_area(id):
    """查看区域详情"""
    area = AdministrativeArea.query.get_or_404(id)

    # 检查权限
    if not current_user.can_access_area(area):
        flash('您没有权限访问此区域', 'danger')
        return redirect(url_for('area.index'))

    # 获取子区域（只显示用户有权限访问的子区域）
    all_children = AdministrativeArea.query.filter_by(parent_id=area.id).order_by(AdministrativeArea.name).all()
    children = [child for child in all_children if current_user.can_access_area(child)]

    # 获取区域用户（只有管理员或区域管理员可以查看）
    if current_user.is_admin() or (current_user.area_id == area.id and current_user.has_permission('user', 'view')):
        users = User.query.filter_by(area_id=area.id).all()
    else:
        users = []

    # 获取区域变更历史（只有管理员可以查看）
    if current_user.is_admin():
        history = AreaChangeHistory.query.filter_by(area_id=area.id).order_by(AreaChangeHistory.created_at.desc()).limit(10).all()
    else:
        history = []

    # 获取区域路径
    area_path = [area]
    ancestors = area.get_ancestors()
    area_path = ancestors + area_path

    return render_template(
        'area/view_area.html',
        title=f'区域详情 - {area.name}',
        area=area,
        children=children,
        users=users,
        history=history,
        area_path=area_path,
        now=datetime.now()
    )

@area_bp.route('/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_area(id):
    """删除区域"""
    area = AdministrativeArea.query.get_or_404(id)

    # 检查是否有子区域
    if area.children:
        flash('无法删除含有子区域的区域，请先删除所有子区域', 'danger')
        return redirect(url_for('area.view_area', id=area.id))

    # 检查是否有关联用户
    if area.users.count() > 0:
        flash('无法删除含有关联用户的区域，请先移除所有关联用户', 'danger')
        return redirect(url_for('area.view_area', id=area.id))

    # 保存区域信息用于日志记录
    area_data = area.to_dict()
    area_name = area.name

    try:
        # 删除区域变更历史
        AreaChangeHistory.query.filter_by(area_id=area.id).delete()

        # 删除区域
        db.session.delete(area)

        # 记录审计日志 - 使用原始SQL避免日期时间精度问题
        audit_sql = text("""
        INSERT INTO audit_logs
        (user_id, action, resource_type, resource_id, details)
        VALUES
        (:user_id, :action, :resource_type, :resource_id, :details)
        """)

        db.session.execute(
            audit_sql,
            {
                'user_id': current_user.id,
                'action': 'delete',
                'resource_type': 'area',
                'resource_id': id,
                'details': json.dumps({
                    'area': area_data
                })
            }
        )
        db.session.commit()

        flash(f'区域 {area_name} 已成功删除', 'success')

        # 如果删除的是当前操作区域，清除会话中的区域ID
        if session.get('current_area_id') == id:
            session.pop('current_area_id', None)
            session.pop('area_switch_time', None)
            session.pop('area_switch_user', None)

        # 重定向到区域管理首页或父区域
        if area.parent_id:
            return redirect(url_for('area.view_area', id=area.parent_id))
        else:
            return redirect(url_for('area.index'))

    except Exception as e:
        db.session.rollback()
        flash(f'删除区域失败: {str(e)}', 'danger')
        return redirect(url_for('area.view_area', id=area.id))

@area_bp.route('/switch/<int:id>')
@login_required
def switch_area(id):
    """切换当前操作区域"""
    area = AdministrativeArea.query.get_or_404(id)

    # 检查权限
    if not current_user.can_access_area(area):
        flash('您没有权限访问此区域', 'danger')
        return redirect(url_for('area.index'))

    # 将当前区域保存到会话中
    session['current_area_id'] = area.id
    # 添加时间戳，用于会话安全检查
    session['area_switch_time'] = datetime.now().timestamp()
    # 添加用户ID，确保会话与用户绑定
    session['area_switch_user'] = current_user.id

    # 记录操作日志
    log_activity(
        action='switch_area',
        description=f'切换到区域: {area.name}',
        resource_type='area',
        resource_id=area.id,
        area_id=area.id
    )

    flash(f'已切换到区域: {area.name}', 'success')

    # 重定向到之前的页面或首页
    next_page = request.args.get('next') or url_for('area.index')
    return redirect(next_page)

@area_bp.route('/tree')
@login_required
def area_tree():
    """获取区域树形结构（用于AJAX请求）"""
    # 如果用户不是管理员，只显示用户所在区域及其下级区域
    if not current_user.is_admin():
        if not current_user.area:
            return jsonify([])

        # 从用户所在区域开始构建树
        areas = [current_user.area]
    else:
        # 管理员可以查看所有顶级区域
        areas = AdministrativeArea.query.filter_by(parent_id=None).all()

    def build_tree(areas):
        result = []
        for area in areas:
            # 检查用户是否有权限访问此区域
            if not current_user.can_access_area(area):
                continue

            area_data = {
                'id': area.id,
                'name': area.name,
                'level': area.level,
                'level_name': area.get_level_name(),
                'code': area.code
            }

            # 递归获取子区域
            children = AdministrativeArea.query.filter_by(parent_id=area.id).all()
            if children:
                area_data['children'] = build_tree(children)

            result.append(area_data)
        return result

    tree = build_tree(areas)
    return jsonify(tree)

@area_bp.route('/users/<int:area_id>')
@login_required
def area_users(area_id):
    """获取区域用户（用于AJAX请求）"""
    area = AdministrativeArea.query.get_or_404(area_id)

    # 检查权限
    if not current_user.can_access_area(area):
        return jsonify({'error': '权限不足'}), 403

    users = User.query.filter_by(area_id=area_id).all()
    return jsonify([user.to_dict() for user in users])

@area_bp.route('/get_areas_by_level_and_parent')
@login_required
def get_areas_by_level_and_parent():
    """根据级别和父级ID获取区域列表（用于AJAX请求）"""
    level = request.args.get('level', type=int)
    parent_id = request.args.get('parent_id', type=int)

    if not level:
        return jsonify({'error': '缺少级别参数'}), 400

    query = AdministrativeArea.query.filter_by(level=level)

    if parent_id:
        query = query.filter_by(parent_id=parent_id)
    elif level > 1:  # 如果是非顶级区域但没有提供父级ID，则返回空列表
        return jsonify([])

    # 只返回用户有权限访问的区域
    areas = []
    for area in query.all():
        if current_user.can_access_area(area):
            areas.append({
                'id': area.id,
                'name': area.name,
                'code': area.code,
                'level': area.level,
                'level_name': area.get_level_name(),
                'is_township_school': area.is_township_school
            })

    return jsonify(areas)

@area_bp.route('/check_db_field')
@login_required
@admin_required
def check_db_field():
    """检查数据库字段是否存在（临时路由，用于验证）"""
    try:
        # 尝试查询一个区域并访问 is_township_school 字段
        area = AdministrativeArea.query.first()
        if area:
            is_field_exists = hasattr(area, 'is_township_school')
            field_value = getattr(area, 'is_township_school', None)
            return jsonify({
                'success': 1,
                'field_exists': is_field_exists,
                'field_value': field_value,
                'area_id': area.id,
                'area_name': area.name
            })
        else:
            return jsonify({
                'success': 0,
                'message': '没有找到任何区域记录'
            })
    except Exception as e:
        return jsonify({
            'success': 0,
            'message': f'检查字段时出错: {str(e)}'
        })
