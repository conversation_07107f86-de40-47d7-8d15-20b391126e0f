from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, DateField, TextAreaField, FloatField, HiddenField, FieldList, FormField, DecimalField
from wtforms.validators import DataRequired, Optional, Length, NumberRange
from datetime import date

class PurchaseOrderItemForm(FlaskForm):
    """采购订单项表单"""
    ingredient_id = HiddenField('食材ID', validators=[Optional()])
    ingredient_name = StringField('食材名称', validators=[DataRequired(), Length(max=100)])
    quantity = FloatField('数量', validators=[DataRequired(), NumberRange(min=0.01)])
    unit = StringField('单位', validators=[DataRequired(), Length(max=20)])
    unit_price = FloatField('单价', validators=[DataRequired(), NumberRange(min=0)])
    total_price = FloatField('总价', validators=[DataRequired(), NumberRange(min=0)])
    supplier_id = SelectField('供应商', coerce=int, validators=[Optional()])
    product_id = HiddenField('产品ID', validators=[Optional()])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=500)])

    class Meta:
        # 禁用CSRF，因为它将作为FormField嵌套在主表单中
        csrf = False

class PurchaseOrderForm(FlaskForm):
    """采购订单表单"""
    area_id = SelectField('区域', coerce=int, validators=[DataRequired()])
    supplier_id = SelectField('供应商', coerce=int, validators=[Optional()])
    order_date = DateField('订单日期', default=date.today, validators=[DataRequired()])
    expected_delivery_date = DateField('预计交付日期', validators=[Optional()])
    batch_number = StringField('批次号', validators=[Optional(), Length(max=50)])
    notes = TextAreaField('备注', validators=[Optional(), Length(max=1000)])
    items = FieldList(FormField(PurchaseOrderItemForm), min_entries=0)

class PurchaseOrderFilterForm(FlaskForm):
    """采购订单筛选表单"""
    area_id = SelectField('区域', coerce=int, validators=[Optional()])
    supplier_id = SelectField('供应商', coerce=int, validators=[Optional()])
    status = SelectField('状态', choices=[
        ('', '全部'),
        ('待确认', '待确认'),
        ('已确认', '已确认'),
        ('部分收货', '部分收货'),
        ('全部收货', '全部收货'),
        ('已取消', '已取消')
    ], validators=[Optional()])
    start_date = DateField('开始日期', validators=[Optional()])
    end_date = DateField('结束日期', validators=[Optional()])

class MenuPurchaseOrderItemForm(FlaskForm):
    """从菜单创建的采购订单项表单"""
    id = HiddenField('食材ID', validators=[DataRequired()])
    purchase_quantity = DecimalField('采购数量', places=2, validators=[DataRequired(), NumberRange(min=0.01)])
    product_id = HiddenField('产品ID', validators=[Optional()])
    supplier_id = HiddenField('供应商ID', validators=[Optional()])

    class Meta:
        csrf = False

class MenuPurchaseOrderForm(FlaskForm):
    """从菜单创建的采购订单表单"""
    area_id = HiddenField('区域ID', validators=[DataRequired()])
    ingredients = FieldList(FormField(MenuPurchaseOrderItemForm), min_entries=0)
