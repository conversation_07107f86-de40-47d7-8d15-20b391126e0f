from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Recipe
from app.models_recipe_advanced import UserRecipeFavorite

recipe_favorite_bp = Blueprint('recipe_favorite', __name__)

@recipe_favorite_bp.route('/favorites')
@login_required
def index():
    """用户收藏的食谱列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 12, type=int)
    
    # 获取用户收藏的食谱
    favorites = UserRecipeFavorite.query.filter_by(user_id=current_user.id).order_by(
        UserRecipeFavorite.created_at.desc()
    ).paginate(page=page, per_page=per_page)
    
    # 获取食谱详情
    favorite_recipes = []
    for favorite in favorites.items:
        recipe = Recipe.query.get(favorite.recipe_id)
        if recipe:
            favorite_recipes.append({
                'recipe': recipe,
                'favorite_id': favorite.recipe_id,
                'favorite_date': favorite.created_at
            })
    
    return render_template('recipe/favorites.html',
                          favorite_recipes=favorite_recipes,
                          pagination=favorites)

@recipe_favorite_bp.route('/api/toggle-favorite/<int:recipe_id>', methods=['POST'])
@login_required
def toggle_favorite(recipe_id):
    """收藏/取消收藏食谱"""
    recipe = Recipe.query.get_or_404(recipe_id)
    
    # 检查是否已收藏
    favorite = UserRecipeFavorite.query.filter_by(
        user_id=current_user.id,
        recipe_id=recipe_id
    ).first()
    
    if favorite:
        # 已收藏，取消收藏
        db.session.delete(favorite)
        db.session.commit()
        return jsonify({
            'status': 'success',
            'action': 'unfavorited',
            'message': '已取消收藏'
        })
    else:
        # 未收藏，添加收藏
        new_favorite = UserRecipeFavorite(
            user_id=current_user.id,
            recipe_id=recipe_id
        )
        db.session.add(new_favorite)
        db.session.commit()
        return jsonify({
            'status': 'success',
            'action': 'favorited',
            'message': '收藏成功'
        })

@recipe_favorite_bp.route('/api/check-favorite/<int:recipe_id>')
@login_required
def check_favorite(recipe_id):
    """检查食谱是否已收藏"""
    favorite = UserRecipeFavorite.query.filter_by(
        user_id=current_user.id,
        recipe_id=recipe_id
    ).first()
    
    return jsonify({
        'is_favorited': favorite is not None
    })
