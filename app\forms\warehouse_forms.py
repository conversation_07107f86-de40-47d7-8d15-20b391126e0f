"""
仓库管理表单

使用Flask-WTF处理仓库管理相关的表单。
"""
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, FloatField, TextAreaField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Optional, Length

class WarehouseForm(FlaskForm):
    """仓库表单"""
    name = StringField('仓库名称', validators=[DataRequired(), Length(max=100)])
    area_id = SelectField('所属区域', coerce=int, validators=[DataRequired()])
    location = StringField('位置', validators=[DataRequired(), Length(max=200)])
    manager_id = SelectField('管理员', coerce=int, validators=[DataRequired()])
    capacity = FloatField('容量', validators=[Optional()])
    capacity_unit = StringField('容量单位', validators=[Optional(), Length(max=20)])
    temperature_range = StringField('温度范围', validators=[Optional(), Length(max=50)])
    humidity_range = StringField('湿度范围', validators=[Optional(), Length(max=50)])
    status = SelectField('状态', choices=[
        ('正常', '正常'),
        ('维护中', '维护中'),
        ('已关闭', '已关闭')
    ], default='正常')
    notes = TextAreaField('备注', validators=[Optional(), Length(max=1000)])
    submit = SubmitField('提交')

class StorageLocationForm(FlaskForm):
    """存储位置表单"""
    warehouse_id = SelectField('所属仓库', coerce=int, validators=[DataRequired()])
    name = StringField('位置名称', validators=[DataRequired(), Length(max=100)])
    location_code = StringField('位置编码', validators=[DataRequired(), Length(max=50)])
    storage_type = SelectField('存储类型', choices=[
        ('常温', '常温'),
        ('冷藏', '冷藏'),
        ('冷冻', '冷冻')
    ], default='常温')
    capacity = FloatField('容量', validators=[Optional()])
    capacity_unit = StringField('容量单位', validators=[Optional(), Length(max=20)])
    temperature_range = StringField('温度范围', validators=[Optional(), Length(max=50)])
    status = SelectField('状态', choices=[
        ('正常', '正常'),
        ('维护中', '维护中'),
        ('已关闭', '已关闭')
    ], default='正常')
    notes = TextAreaField('备注', validators=[Optional(), Length(max=1000)])
    submit = SubmitField('提交')
