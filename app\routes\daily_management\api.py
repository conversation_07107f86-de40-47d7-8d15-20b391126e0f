from flask import jsonify, request, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.daily_management import daily_management_bp
from app.models_daily_management import (
    DailyLog, InspectionRecord, DiningCompanion,
    CanteenTrainingRecord, SpecialEvent, Issue
)
from app.services.daily_management_service import DailyLogService
from datetime import datetime, date
from sqlalchemy import func
import os
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64

# API路由前缀
API_PREFIX = '/api'

# 获取日志列表
@daily_management_bp.route(f'{API_PREFIX}/logs')
@login_required
def api_get_logs():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)

    # 获取用户所属学校，确保数据隔离
    user_area = current_user.get_current_area()
    if not user_area:
        return jsonify({'error': '用户没有关联学校'}), 403

    logs_query = DailyLog.query.filter_by(area_id=user_area.id).order_by(DailyLog.log_date.desc())

    # 日期范围过滤
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    if start_date:
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            logs_query = logs_query.filter(DailyLog.log_date >= start_date)
        except ValueError:
            pass

    if end_date:
        try:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            logs_query = logs_query.filter(DailyLog.log_date <= end_date)
        except ValueError:
            pass

    # 分页
    logs_pagination = logs_query.paginate(page=page, per_page=per_page, error_out=False)

    logs_data = []
    for log in logs_pagination.items:
        logs_data.append({
            'id': log.id,
            'log_date': log.log_date.strftime('%Y-%m-%d'),
            'weather': log.weather,
            'manager': log.manager,
            'student_count': log.student_count,
            'teacher_count': log.teacher_count,
            'other_count': log.other_count,
            'food_waste': log.food_waste,
            'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify({
        'logs': logs_data,
        'total': logs_pagination.total,
        'pages': logs_pagination.pages,
        'current_page': logs_pagination.page
    })

# 获取单个日志详情
@daily_management_bp.route(f'{API_PREFIX}/logs/<int:log_id>')
@login_required
def api_get_log(log_id):
    # 确保用户只能访问自己学校的日志
    user_area = current_user.get_current_area()
    if not user_area:
        return jsonify({'error': '用户没有关联学校'}), 403

    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    log_data = {
        'id': log.id,
        'log_date': log.log_date.strftime('%Y-%m-%d'),
        'weather': log.weather,
        'manager': log.manager,
        'student_count': log.student_count,
        'teacher_count': log.teacher_count,
        'other_count': log.other_count,
        'breakfast_menu': log.breakfast_menu,
        'lunch_menu': log.lunch_menu,
        'dinner_menu': log.dinner_menu,
        'food_waste': log.food_waste,
        'special_events': log.special_events,
        'operation_summary': log.operation_summary,
        'created_at': log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'updated_at': log.updated_at.strftime('%Y-%m-%d %H:%M:%S') if log.updated_at else None
    }

    return jsonify(log_data)

# 创建或更新日志
@daily_management_bp.route(f'{API_PREFIX}/logs', methods=['POST'])
@login_required
def api_create_update_log():
    data = request.json

    try:
        # 处理日期时间参数
        try:
            log_date = datetime.strptime(data.get('log_date'), '%Y-%m-%d').replace(microsecond=0)
        except (ValueError, TypeError):
            return jsonify({'error': '日期格式无效'}), 400

        # 获取日志
        log = DailyLogService.get_daily_log_by_date(log_date)

        if log is None:
            # 创建新日志
            data['log_date'] = log_date  # 使用处理后的日期时间对象
            data['created_by'] = current_user.id
            log = DailyLogService.create_daily_log(data)
        else:
            # 更新现有日志
            log = DailyLogService.update_daily_log(log.id, data)

        if not log:
            return jsonify({'error': '保存失败'}), 500

        return jsonify({
            'success': True,
            'id': log.id,
            'message': '日志保存成功'
        })

    except Exception as e:
        return jsonify({'error': f'保存失败: {str(e)}'}), 500

# 获取检查记录
@daily_management_bp.route(f'{API_PREFIX}/inspections/<int:log_id>')
@login_required
def api_get_inspections(log_id):
    # 确保用户只能访问自己学校的检查记录
    user_area = current_user.get_current_area()
    if not user_area:
        return jsonify({'error': '用户没有关联学校'}), 403

    log = DailyLog.query.filter_by(id=log_id, area_id=user_area.id).first_or_404()

    inspection_type = request.args.get('type')
    if inspection_type and inspection_type in ['morning', 'noon', 'evening']:
        inspections = InspectionRecord.query.filter_by(
            daily_log_id=log_id,
            inspection_type=inspection_type
        ).all()
    else:
        inspections = InspectionRecord.query.filter_by(daily_log_id=log_id).all()

    inspections_data = []
    for inspection in inspections:
        # 获取照片
        photos = []
        if inspection.photo_path:
            photos.append({
                'file_path': inspection.photo_path
            })

        inspections_data.append({
            'id': inspection.id,
            'inspection_type': inspection.inspection_type,
            'inspection_item': inspection.inspection_item,
            'status': inspection.status,
            'description': inspection.description,
            'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S'),
            'photos': photos
        })

    return jsonify(inspections_data)

# 创建检查记录
@daily_management_bp.route(f'{API_PREFIX}/inspections', methods=['POST'])
@login_required
def api_create_inspection():
    data = request.json

    log_id = data.get('log_id')
    inspection_type = data.get('inspection_type')
    inspection_item = data.get('inspection_item')
    status = data.get('status', 'normal')
    description = data.get('description')

    if not all([log_id, inspection_type, inspection_item]):
        return jsonify({'error': '缺少必要参数'}), 400

    if inspection_type not in ['morning', 'noon', 'evening']:
        return jsonify({'error': '检查类型无效'}), 400

    # 查找是否已存在该检查项
    record = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type=inspection_type,
        inspection_item=inspection_item
    ).first()

    if record is None:
        record = InspectionRecord(
            daily_log_id=log_id,
            inspection_type=inspection_type,
            inspection_item=inspection_item,
            inspector_id=current_user.id
        )
        db.session.add(record)

    record.status = status
    record.description = description

    try:
        db.session.commit()
        return jsonify({
            'success': True,
            'id': record.id,
            'message': '检查记录保存成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'保存失败: {str(e)}'}), 500

# 上传检查照片
@daily_management_bp.route(f'{API_PREFIX}/upload-photo', methods=['POST'])
@login_required
def api_upload_photo():
    if 'photo' not in request.files:
        return jsonify({'error': '没有文件'}), 400

    photo_file = request.files['photo']
    if not photo_file.filename:
        return jsonify({'error': '没有选择文件'}), 400

    reference_id = request.form.get('reference_id', type=int)
    reference_type = request.form.get('reference_type')

    if not reference_id or not reference_type:
        return jsonify({'error': '缺少必要参数'}), 400

    if reference_type not in ['inspection', 'companion', 'training', 'event', 'issue']:
        return jsonify({'error': '引用类型无效'}), 400

    try:
        # 根据引用类型获取对应的记录
        record = None
        if reference_type == 'inspection':
            record = InspectionRecord.query.get_or_404(reference_id)
        elif reference_type == 'companion':
            record = DiningCompanion.query.get_or_404(reference_id)
        elif reference_type == 'training':
            record = CanteenTrainingRecord.query.get_or_404(reference_id)
        elif reference_type == 'event':
            record = SpecialEvent.query.get_or_404(reference_id)
        elif reference_type == 'issue':
            record = Issue.query.get_or_404(reference_id)

        if not record:
            return jsonify({'error': '记录不存在'}), 404

        # 上传照片
        photo_path = handle_photo_upload(photo_file, reference_type)

        if not photo_path:
            return jsonify({'error': '照片上传失败'}), 500

        # 更新记录的照片路径
        if reference_type == 'inspection':
            record.photo_path = photo_path
        else:
            # 对于其他类型，使用 photo_paths 字段
            photo_paths = []
            if record.photo_paths:
                photo_paths = record.photo_paths.split(';')
            photo_paths.append(photo_path)
            record.photo_paths = ';'.join(photo_paths)

        db.session.commit()

        return jsonify({
            'success': True,
            'file_path': photo_path,
            'message': '照片上传成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

# 获取统计数据
@daily_management_bp.route(f'{API_PREFIX}/statistics')
@login_required
def api_get_statistics():
    # 日期范围过滤
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    try:
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        else:
            # 默认过去30天
            start_date = date.today().replace(day=1)

        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        else:
            end_date = date.today()
    except ValueError:
        return jsonify({'error': '日期格式无效'}), 400

    # 基础查询 - 不再使用
    # DailyLog.query.filter(
    #     DailyLog.log_date >= start_date,
    #     DailyLog.log_date <= end_date
    # )

    # 获取用户所属学校，确保数据隔离
    user_area = current_user.get_current_area()
    if not user_area:
        return jsonify({'error': '用户没有关联学校'}), 403

    # 就餐人数统计 - 按学校筛选
    student_count = db.session.query(func.sum(DailyLog.student_count)).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).scalar() or 0

    teacher_count = db.session.query(func.sum(DailyLog.teacher_count)).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).scalar() or 0

    other_count = db.session.query(func.sum(DailyLog.other_count)).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).scalar() or 0

    # 食物浪费统计 - 按学校筛选
    food_waste = db.session.query(func.sum(DailyLog.food_waste)).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).scalar() or 0

    # 检查记录统计 - 按学校筛选
    inspection_stats = db.session.query(
        InspectionRecord.status,
        func.count(InspectionRecord.id)
    ).join(DailyLog).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).group_by(InspectionRecord.status).all()

    inspection_data = {status: count for status, count in inspection_stats}

    # 问题记录统计 - 按学校筛选
    issue_stats = db.session.query(
        Issue.status,
        func.count(Issue.id)
    ).join(DailyLog).filter(
        DailyLog.log_date >= start_date,
        DailyLog.log_date <= end_date,
        DailyLog.area_id == user_area.id
    ).group_by(Issue.status).all()

    issue_data = {status: count for status, count in issue_stats}

    return jsonify({
        'period': {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d')
        },
        'dining_counts': {
            'student': student_count,
            'teacher': teacher_count,
            'other': other_count,
            'total': student_count + teacher_count + other_count
        },
        'food_waste': food_waste,
        'inspections': {
            'normal': inspection_data.get('normal', 0),
            'abnormal': inspection_data.get('abnormal', 0),
            'total': sum(inspection_data.values())
        },
        'issues': {
            'pending': issue_data.get('pending', 0),
            'fixing': issue_data.get('fixing', 0),
            'fixed': issue_data.get('fixed', 0),
            'total': sum(issue_data.values())
        }
    })

# 辅助函数
def handle_photo_upload(photo_file, reference_type):
    """处理照片上传，返回照片路径"""
    if photo_file and photo_file.filename:
        filename = secure_filename(photo_file.filename)
        # 生成唯一文件名
        unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

        # 确保目录存在
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            reference_type
        )
        os.makedirs(upload_folder, exist_ok=True)

        # 处理图片
        img = Image.open(photo_file)

        # 调整大小为800x600，保持宽高比
        img.thumbnail((800, 600))

        # 保存处理后的图片
        file_path = os.path.join(upload_folder, unique_filename)
        img.save(file_path)

        # 返回照片路径
        return f"/static/uploads/daily_management/{reference_type}/{unique_filename}"

    return None
