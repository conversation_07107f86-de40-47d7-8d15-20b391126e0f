# -*- coding: utf-8 -*-
"""
资源配置文件
用于管理项目中的CSS、JS等外部资源
支持本地资源和CDN资源的切换
"""

import os
from flask import current_app

class ResourceConfig:
    """资源配置类"""
    
    def __init__(self):
        # 从环境变量或配置中读取是否使用CDN
        self.use_cdn = os.environ.get('USE_CDN', 'true').lower() == 'true'
        
    def get_css_resources(self):
        """获取CSS资源配置"""
        if self.use_cdn:
            return {
                'tailwind': 'https://cdn.tailwindcss.com',
                'fontawesome': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css',
                'bootstrap': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
                'animate': 'https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css'
            }
        else:
            return {
                'tailwind': '/static/css/tailwind-custom.css',
                'fontawesome': '/static/css/all.min.css',
                'bootstrap': '/static/css/bootstrap.min.css',
                'animate': '/static/css/animate.min.css'
            }
    
    def get_js_resources(self):
        """获取JavaScript资源配置"""
        if self.use_cdn:
            return {
                'chartjs': 'https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js',
                'jquery': 'https://code.jquery.com/jquery-3.7.1.min.js',
                'bootstrap': 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
                'alpinejs': 'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js',
                'axios': 'https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js'
            }
        else:
            return {
                'chartjs': '/static/js/chart.umd.min.js',
                'jquery': '/static/js/jquery-3.7.1.min.js',
                'bootstrap': '/static/js/bootstrap.bundle.min.js',
                'alpinejs': '/static/js/alpine.min.js',
                'axios': '/static/js/axios.min.js'
            }
    
    def get_font_resources(self):
        """获取字体资源配置"""
        if self.use_cdn:
            return {
                'google_fonts': 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap',
                'chinese_fonts': 'https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap'
            }
        else:
            return {
                'google_fonts': '/static/fonts/inter.css',
                'chinese_fonts': '/static/fonts/noto-sans-sc.css'
            }
    
    def get_image_resources(self):
        """获取图片资源配置"""
        if self.use_cdn:
            return {
                'placeholder': 'https://picsum.photos',
                'icons': 'https://cdn.jsdelivr.net/npm/heroicons@2.0.18/24/outline',
                'avatars': 'https://ui-avatars.com/api'
            }
        else:
            return {
                'placeholder': '/static/images/placeholder',
                'icons': '/static/images/icons',
                'avatars': '/static/images/avatars'
            }

# 全局资源配置实例
resource_config = ResourceConfig()

def get_resource_urls():
    """获取所有资源URL的便捷函数"""
    return {
        'css': resource_config.get_css_resources(),
        'js': resource_config.get_js_resources(),
        'fonts': resource_config.get_font_resources(),
        'images': resource_config.get_image_resources(),
        'use_cdn': resource_config.use_cdn
    }

def switch_to_cdn():
    """切换到CDN资源"""
    resource_config.use_cdn = True
    
def switch_to_local():
    """切换到本地资源"""
    resource_config.use_cdn = False

def toggle_resource_mode():
    """切换资源模式"""
    resource_config.use_cdn = not resource_config.use_cdn
    return resource_config.use_cdn
