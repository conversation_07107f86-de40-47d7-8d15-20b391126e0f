from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.models import Supplier, AdministrativeArea, SupplierSchoolRelation
from app.forms.supplier import SupplierSchoolRelationForm
from app.utils.log_activity import log_activity
from datetime import datetime
from sqlalchemy import text

supplier_school_bp = Blueprint('supplier_school', __name__)

@supplier_school_bp.route('/')
@login_required
def index():
    """供应商-学校关系列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    supplier_id = request.args.get('supplier_id', type=int)
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', type=int)

    # 基本查询
    query = SupplierSchoolRelation.query

    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)

    if area_id:
        query = query.filter_by(area_id=area_id)

    if status is not None:
        query = query.filter_by(status=status)

    # 根据用户区域权限筛选关联
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 筛选用户有权限访问的区域关联
        query = query.filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))

    pagination = query.order_by(SupplierSchoolRelation.id.desc()).paginate(page=page, per_page=per_page)
    relations = pagination.items

    # 获取供应商列表，也需要根据用户区域权限筛选
    if current_user.is_admin():
        suppliers = Supplier.query.filter_by(status=1).all()  # 只显示合作中的供应商
    else:
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .distinct().all()

    # 获取学校列表，也需要根据用户区域权限筛选
    if current_user.is_admin():
        # 只获取学校级别的区域
        schools = AdministrativeArea.query.filter_by(level=3).all()  # 假设level=3表示学校级别
    else:
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 筛选用户有权限访问的学校
        schools = AdministrativeArea.query.filter(
            AdministrativeArea.level == 3,  # 学校级别
            AdministrativeArea.id.in_(accessible_area_ids)
        ).all()

    # 获取当前用户的区域信息
    current_area = current_user.get_current_area()
    area_path = []
    if current_area:
        area_path = [current_area]
        ancestors = current_area.get_ancestors()
        area_path = ancestors + area_path

    return render_template('supplier/school_index.html',
                          relations=relations,
                          pagination=pagination,
                          suppliers=suppliers,
                          areas=schools,
                          supplier_id=supplier_id,
                          area_id=area_id,
                          status=status,
                          current_area=current_area,
                          area_path=area_path,
                          title='供应商-学校关系管理',
                          now=datetime.now())

@supplier_school_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """添加供应商-学校关系"""
    form = SupplierSchoolRelationForm()

    # 获取供应商选项，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .distinct().all()

    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    # 获取学校选项，根据用户区域权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有学校
        schools = AdministrativeArea.query.filter_by(level=3).all()  # 假设level=3表示学校级别
    else:
        # 普通用户只能看到自己区域内的学校
        accessible_areas = current_user.get_accessible_areas()
        schools = [area for area in accessible_areas if area.level == 3]  # 只保留学校级别的区域

    # 构建选项列表
    form.area_id.choices = [(0, '-- 请选择学校 --')] + [(s.id, s.name) for s in schools]

    if form.validate_on_submit():
        # 检查是否已存在相同的供应商-学校关系
        existing = SupplierSchoolRelation.query.filter_by(
            supplier_id=form.supplier_id.data,
            area_id=form.area_id.data,
            status=1  # 有效状态
        ).first()

        if existing:
            flash('该供应商与学校已存在有效的合作关系！', 'danger')
            return redirect(url_for('supplier_school.index'))

        try:
            # 使用原始SQL创建记录，避免SQLAlchemy的日期时间处理问题
            end_date_value = f"'{form.end_date.data}'" if form.end_date.data else "NULL"
            notes_value = f"'{form.notes.data}'" if form.notes.data else "NULL"
            contract_number_value = f"'{form.contract_number.data}'" if form.contract_number.data else "NULL"

            # 使用系统自带的会话执行SQL
            result = db.session.execute(text(f"""
            INSERT INTO supplier_school_relations
            (supplier_id, area_id, contract_number, start_date, end_date, status, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES
            ({form.supplier_id.data}, {form.area_id.data}, {contract_number_value}, '{form.start_date.data}',
            {end_date_value}, {form.status.data}, {notes_value}, GETDATE(), GETDATE())
            """))

            # 获取新插入记录的ID
            relation_id = result.fetchone()[0]

            # 查询新创建的记录
            relation = SupplierSchoolRelation.query.get(relation_id)

            # 提交事务
            db.session.commit()

            # 使用系统自带的log_activity函数记录审计日志
            log_activity(
                action='create',
                resource_type='SupplierSchoolRelation',
                resource_id=relation.id,
                area_id=relation.area_id,
                details={
                    'supplier_id': relation.supplier_id,
                    'area_id': relation.area_id,
                    'contract_number': relation.contract_number,
                    'start_date': relation.start_date.strftime('%Y-%m-%d'),
                    'end_date': relation.end_date.strftime('%Y-%m-%d') if relation.end_date else None,
                    'status': relation.status
                }
            )

            flash('供应商-学校关系添加成功！', 'success')
            return redirect(url_for('supplier_school.index'))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建供应商-学校关系失败: {str(e)}")
            flash(f'创建供应商-学校关系失败: {str(e)}', 'danger')
            return redirect(url_for('supplier_school.index'))

    return render_template('supplier/school_form.html', form=form, title='添加供应商-学校关系', now=datetime.now())

@supplier_school_bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑供应商-学校关系"""
    relation = SupplierSchoolRelation.query.get_or_404(id)
    form = SupplierSchoolRelationForm(obj=relation)

    # 获取供应商选项，根据用户权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有供应商
        suppliers = Supplier.query.filter_by(status=1).all()
    else:
        # 普通用户只能看到与自己管辖学校有合作关系的供应商
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 通过供应商-学校关联表筛选供应商
        suppliers = Supplier.query.join(SupplierSchoolRelation)\
                    .filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))\
                    .filter(Supplier.status == 1)\
                    .distinct().all()

    form.supplier_id.choices = [(0, '-- 请选择供应商 --')] + [(s.id, s.name) for s in suppliers]

    # 获取学校选项，根据用户区域权限筛选
    if current_user.is_admin():
        # 系统管理员可以看到所有学校
        schools = AdministrativeArea.query.filter_by(level=3).all()  # 假设level=3表示学校级别
    else:
        # 普通用户只能看到自己区域内的学校
        accessible_areas = current_user.get_accessible_areas()
        schools = [area for area in accessible_areas if area.level == 3]  # 只保留学校级别的区域

    # 构建选项列表
    form.area_id.choices = [(0, '-- 请选择学校 --')] + [(s.id, s.name) for s in schools]

    if form.validate_on_submit():
        old_data = {
            'supplier_id': relation.supplier_id,
            'area_id': relation.area_id,
            'contract_number': relation.contract_number,
            'start_date': relation.start_date.strftime('%Y-%m-%d'),
            'end_date': relation.end_date.strftime('%Y-%m-%d') if relation.end_date else None,
            'status': relation.status,
            'notes': relation.notes
        }

        relation.supplier_id = form.supplier_id.data
        relation.area_id = form.area_id.data
        relation.contract_number = form.contract_number.data
        relation.start_date = form.start_date.data
        relation.end_date = form.end_date.data
        relation.status = form.status.data
        relation.notes = form.notes.data

        # 使用系统自带的log_activity函数记录审计日志
        log_activity(
            action='update',
            resource_type='SupplierSchoolRelation',
            resource_id=relation.id,
            area_id=relation.area_id,
            details={
                'old': old_data,
                'new': {
                    'supplier_id': relation.supplier_id,
                    'area_id': relation.area_id,
                    'contract_number': relation.contract_number,
                    'start_date': relation.start_date.strftime('%Y-%m-%d'),
                    'end_date': relation.end_date.strftime('%Y-%m-%d') if relation.end_date else None,
                    'status': relation.status,
                    'notes': relation.notes
                }
            }
        )

        db.session.commit()
        flash('供应商-学校关系更新成功！', 'success')
        return redirect(url_for('supplier_school.index'))

    return render_template('supplier/school_form.html', form=form, relation=relation, title='编辑供应商-学校关系', now=datetime.now())

@supplier_school_bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除供应商-学校关系"""
    current_app.logger.debug(f"尝试删除供应商-学校关系 ID: {id}, 用户: {current_user.username}")

    try:
        relation = SupplierSchoolRelation.query.get_or_404(id)

        # 检查用户是否有权限删除
        if not current_user.is_admin() and not current_user.can_access_area_by_id(relation.area_id):
            current_app.logger.warning(f"用户 {current_user.username} 尝试删除无权限的供应商-学校关系 ID: {id}")
            return jsonify({'success': 0, 'message': '您没有权限删除该供应商-学校关系'}), 403

        # 使用系统自带的log_activity函数记录审计日志
        log_activity(
            action='delete',
            resource_type='SupplierSchoolRelation',
            resource_id=relation.id,
            area_id=relation.area_id,
            details={
                'supplier_id': relation.supplier_id,
                'supplier_name': relation.supplier.name,
                'area_id': relation.area_id,
                'area_name': relation.area.name,
                'contract_number': relation.contract_number,
                'start_date': relation.start_date.strftime('%Y-%m-%d'),
                'end_date': relation.end_date.strftime('%Y-%m-%d') if relation.end_date else None,
                'status': relation.status
            }
        )

        db.session.delete(relation)
        db.session.commit()

        current_app.logger.info(f"成功删除供应商-学校关系 ID: {id}, 用户: {current_user.username}")
        return jsonify({'success': 1, 'message': '供应商-学校关系删除成功！'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除供应商-学校关系失败 ID: {id}, 错误: {str(e)}")
        return jsonify({'success': 0, 'message': f'删除失败: {str(e)}'}), 500

@supplier_school_bp.route('/terminate/<int:id>', methods=['POST'])
@login_required
def terminate(id):
    """终止供应商-学校关系"""
    current_app.logger.debug(f"尝试终止供应商-学校关系 ID: {id}, 用户: {current_user.username}")

    try:
        relation = SupplierSchoolRelation.query.get_or_404(id)

        # 检查用户是否有权限终止
        if not current_user.is_admin() and not current_user.can_access_area_by_id(relation.area_id):
            current_app.logger.warning(f"用户 {current_user.username} 尝试终止无权限的供应商-学校关系 ID: {id}")
            return jsonify({'success': 0, 'message': '您没有权限终止该供应商-学校关系'}), 403

        # 检查当前状态
        if relation.status == 0:
            return jsonify({'success': 0, 'message': '该供应商-学校关系已经是终止状态'}), 400

        # 更新状态为已终止
        relation.status = 0

        # 使用系统自带的log_activity函数记录审计日志
        log_activity(
            action='terminate',
            resource_type='SupplierSchoolRelation',
            resource_id=relation.id,
            area_id=relation.area_id,
            details={
                'supplier_id': relation.supplier_id,
                'supplier_name': relation.supplier.name,
                'area_id': relation.area_id,
                'area_name': relation.area.name,
                'terminate_time': datetime.now().strftime("%Y-%m-%d %H:%M")
            }
        )

        db.session.commit()
        current_app.logger.info(f"成功终止供应商-学校关系 ID: {id}, 用户: {current_user.username}")
        return jsonify({'success': 1, 'message': '供应商-学校关系已终止！'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"终止供应商-学校关系失败 ID: {id}, 错误: {str(e)}")
        return jsonify({'success': 0, 'message': f'终止失败: {str(e)}'}), 500

@supplier_school_bp.route('/activate/<int:id>', methods=['POST'])
@login_required
def activate(id):
    """激活供应商-学校关系"""
    current_app.logger.debug(f"尝试激活供应商-学校关系 ID: {id}, 用户: {current_user.username}")

    try:
        relation = SupplierSchoolRelation.query.get_or_404(id)

        # 检查用户是否有权限激活
        if not current_user.is_admin() and not current_user.can_access_area_by_id(relation.area_id):
            current_app.logger.warning(f"用户 {current_user.username} 尝试激活无权限的供应商-学校关系 ID: {id}")
            return jsonify({'success': 0, 'message': '您没有权限激活该供应商-学校关系'}), 403

        # 检查当前状态
        if relation.status == 1:
            return jsonify({'success': 0, 'message': '该供应商-学校关系已经是激活状态'}), 400

        # 更新状态为有效
        relation.status = 1

        # 使用系统自带的log_activity函数记录审计日志
        log_activity(
            action='activate',
            resource_type='SupplierSchoolRelation',
            resource_id=relation.id,
            area_id=relation.area_id,
            details={
                'supplier_id': relation.supplier_id,
                'supplier_name': relation.supplier.name,
                'area_id': relation.area_id,
                'area_name': relation.area.name,
                'activate_time': datetime.now().strftime("%Y-%m-%d %H:%M")
            }
        )

        db.session.commit()
        current_app.logger.info(f"成功激活供应商-学校关系 ID: {id}, 用户: {current_user.username}")
        return jsonify({'success': 1, 'message': '供应商-学校关系已激活！'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"激活供应商-学校关系失败 ID: {id}, 错误: {str(e)}")
        return jsonify({'success': 0, 'message': f'激活失败: {str(e)}'}), 500

@supplier_school_bp.route('/api/suppliers_by_school/<int:school_id>')
@login_required
def api_suppliers_by_school(school_id):
    """获取学校的合作供应商"""
    # 检查用户是否有权限访问该学校
    if not current_user.is_admin():
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]
        if school_id not in accessible_area_ids:
            return jsonify({'error': '您没有权限访问此学校的数据'}), 403

    relations = SupplierSchoolRelation.query.filter_by(area_id=school_id, status=1).all()
    suppliers = []

    for relation in relations:
        suppliers.append({
            'id': relation.supplier.id,
            'name': relation.supplier.name,
            'contact_person': relation.supplier.contact_person,
            'phone': relation.supplier.phone,
            'relation_id': relation.id,
            'contract_number': relation.contract_number,
            'start_date': relation.start_date.strftime('%Y-%m-%d')
        })

    return jsonify(suppliers)

@supplier_school_bp.route('/api/schools_by_supplier/<int:supplier_id>')
@login_required
def api_schools_by_supplier(supplier_id):
    """获取供应商的合作学校"""
    # 基本查询
    query = SupplierSchoolRelation.query.filter_by(supplier_id=supplier_id, status=1)

    # 根据用户区域权限筛选学校
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_area_ids = [area.id for area in current_user.get_accessible_areas()]

        # 筛选用户有权限访问的区域关联
        query = query.filter(SupplierSchoolRelation.area_id.in_(accessible_area_ids))

    relations = query.all()
    schools = []

    for relation in relations:
        schools.append({
            'id': relation.area.id,
            'name': relation.area.name,
            'relation_id': relation.id,
            'contract_number': relation.contract_number,
            'start_date': relation.start_date.strftime('%Y-%m-%d')
        })

    return jsonify(schools)
