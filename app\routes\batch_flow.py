from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import AuditLog
from app.models_ingredient_traceability import MaterialBatch, BatchFlow
from app import db
from datetime import datetime
import json

batch_flow_bp = Blueprint('batch_flow', __name__)

@batch_flow_bp.route('/batch-flow/create/<int:batch_id>', methods=['GET', 'POST'])
@login_required
def create(batch_id):
    """创建批次流水记录"""
    batch = MaterialBatch.query.get_or_404(batch_id)
    
    # 检查用户是否有权限操作该批次
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限操作该批次', 'danger')
        return redirect(url_for('material_batch.view', id=batch_id))
    
    if request.method == 'POST':
        # 获取表单数据
        flow_type = request.form.get('flow_type')
        flow_direction = request.form.get('flow_direction')
        quantity = request.form.get('quantity', type=float)
        unit = request.form.get('unit')
        related_id = request.form.get('related_id', type=int)
        related_type = request.form.get('related_type')
        remark = request.form.get('remark')
        
        # 验证必填字段
        if not all([flow_type, flow_direction, quantity, unit]):
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('batch_flow.create', batch_id=batch_id))
        
        # 验证数量
        if quantity <= 0:
            flash('数量必须大于0', 'danger')
            return redirect(url_for('batch_flow.create', batch_id=batch_id))
        
        # 验证单位
        if unit != batch.unit:
            flash(f'单位必须与批次单位一致: {batch.unit}', 'danger')
            return redirect(url_for('batch_flow.create', batch_id=batch_id))
        
        # 计算新的库存量
        new_quantity = batch.current_quantity
        if flow_direction == '增加':
            new_quantity += quantity
        else:  # 减少
            new_quantity -= quantity
            # 检查库存是否足够
            if new_quantity < 0:
                flash('库存不足，无法减少', 'danger')
                return redirect(url_for('batch_flow.create', batch_id=batch_id))
        
        # 创建流水记录
        flow = BatchFlow(
            batch_id=batch_id,
            flow_type=flow_type,
            flow_direction=flow_direction,
            quantity=quantity,
            unit=unit,
            related_id=related_id,
            related_type=related_type,
            operator_id=current_user.id,
            flow_date=datetime.now(),
            remark=remark
        )
        
        db.session.add(flow)
        
        # 更新批次库存量
        batch.current_quantity = new_quantity
        
        # 更新批次状态
        if new_quantity == 0:
            batch.status = '已用完'
        elif new_quantity < batch.initial_quantity * 0.2:  # 低于20%预警
            batch.status = '预警'
        else:
            batch.status = '正常'
        
        # 添加审计日志 - 使用原始SQL避免日期时间精度问题
        from app.utils.log_activity import log_activity
        db.session.commit()  # 先提交以获取flow.id

        log_activity(
            action='create',
            resource_type='BatchFlow',
            resource_id=flow.id,
            details={
                'flow': flow.to_dict(),
                'batch_before': {
                    'id': batch.id,
                    'current_quantity': batch.current_quantity + (quantity if flow_direction == '减少' else -quantity),
                    'status': batch.status
                },
                'batch_after': {
                    'id': batch.id,
                    'current_quantity': batch.current_quantity,
                    'status': batch.status
                }
            }
        )
        
        db.session.commit()
        flash('批次流水记录创建成功！', 'success')
        return redirect(url_for('material_batch.view', id=batch_id))
    
    return render_template('batch_flow/form.html', batch=batch)

@batch_flow_bp.route('/batch-flow/<int:id>')
@login_required
def view(id):
    """查看批次流水详情"""
    flow = BatchFlow.query.get_or_404(id)
    batch = MaterialBatch.query.get(flow.batch_id)
    
    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限查看该流水记录', 'danger')
        return redirect(url_for('material_batch.index'))
    
    return render_template('batch_flow/view.html', flow=flow, batch=batch)

@batch_flow_bp.route('/batch-flow/list/<int:batch_id>')
@login_required
def list_flows(batch_id):
    """查看批次的所有流水记录"""
    batch = MaterialBatch.query.get_or_404(batch_id)
    
    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限查看该批次', 'danger')
        return redirect(url_for('material_batch.index'))
    
    # 获取批次流水
    flows = BatchFlow.query.filter_by(batch_id=batch_id).order_by(BatchFlow.flow_date.desc()).all()
    
    return render_template('batch_flow/list.html', batch=batch, flows=flows)
