from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Warehouse, StorageLocation, AdministrativeArea, User
from app import db
from datetime import datetime
import json
from sqlalchemy import text

warehouse_bp = Blueprint('warehouse', __name__)

@warehouse_bp.route('/warehouse')
@login_required
def index():
    """仓库列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    status = request.args.get('status', '')
    area_id = request.args.get('area_id', type=int)

    # 构建查询
    query = Warehouse.query.filter(Warehouse.area_id.in_(area_ids))

    # 应用过滤条件
    if status:
        query = query.filter(Warehouse.status == status)
    if area_id:
        query = query.filter(Warehouse.area_id == area_id)

    # 按创建时间降序排序
    query = query.order_by(Warehouse.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    warehouses = pagination.items

    # 获取区域列表，用于筛选
    areas = accessible_areas

    return render_template('warehouse/index.html',
                          warehouses=warehouses,
                          pagination=pagination,
                          areas=areas,
                          status=status,
                          area_id=area_id)

@warehouse_bp.route('/warehouse/create', methods=['GET', 'POST'])
@login_required
def create():
    """创建仓库"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    if request.method == 'POST':
        # 获取表单数据
        name = request.form.get('name')
        area_id = request.form.get('area_id', type=int)
        location = request.form.get('location')
        manager_id = request.form.get('manager_id', type=int)
        capacity = request.form.get('capacity', type=float)
        capacity_unit = request.form.get('capacity_unit')
        temperature_range = request.form.get('temperature_range')
        humidity_range = request.form.get('humidity_range')
        status = request.form.get('status')
        notes = request.form.get('notes')

        # 验证数据
        if not name or not area_id or not location or not manager_id:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('warehouse.create'))

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect(url_for('warehouse.index'))

        # 检查该区域是否已有仓库
        existing_warehouse = Warehouse.query.filter_by(area_id=area_id).first()
        if existing_warehouse:
            flash('该区域已有仓库，不能重复创建', 'warning')
            return redirect(url_for('warehouse.index'))

        # 使用原始SQL语句创建仓库，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含created_at和updated_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO warehouses (name, area_id, location, manager_id, capacity, capacity_unit,
                                   temperature_range, humidity_range, status, notes)
            OUTPUT inserted.id
            VALUES (:name, :area_id, :location, :manager_id, :capacity, :capacity_unit,
                   :temperature_range, :humidity_range, :status, :notes)
            """)

            # 执行SQL语句
            result = db.session.execute(sql,
                {"name": name, "area_id": area_id, "location": location, "manager_id": manager_id,
                 "capacity": capacity, "capacity_unit": capacity_unit, "temperature_range": temperature_range,
                 "humidity_range": humidity_range, "status": status, "notes": notes}
            )

            # 获取新创建的仓库ID
            warehouse_id = result.fetchone()[0]

            # 提交事务
            db.session.commit()

            flash('仓库创建成功', 'success')
            return redirect(url_for('warehouse.view', id=warehouse_id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建仓库时出错: {str(e)}")
            flash(f'创建仓库时出错: {str(e)}', 'danger')
            return redirect(url_for('warehouse.create'))

    # GET请求，显示创建表单
    # 获取区域列表
    areas = accessible_areas

    # 获取可选的管理员列表
    managers = User.query.filter(User.area_id.in_([area.id for area in accessible_areas])).all()

    return render_template('warehouse/form.html',
                          warehouse=None,
                          areas=areas,
                          managers=managers,
                          title='创建仓库')

@warehouse_bp.route('/warehouse/<int:id>')
@login_required
def view(id):
    """查看仓库详情"""
    warehouse = Warehouse.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限查看该仓库', 'danger')
        return redirect(url_for('warehouse.index'))

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=id).all()

    return render_template('warehouse/view.html',
                          warehouse=warehouse,
                          storage_locations=storage_locations)

@warehouse_bp.route('/warehouse/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑仓库"""
    warehouse = Warehouse.query.get_or_404(id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限编辑该仓库', 'danger')
        return redirect(url_for('warehouse.index'))

    if request.method == 'POST':
        # 获取表单数据
        name = request.form.get('name')
        location = request.form.get('location')
        manager_id = request.form.get('manager_id', type=int)
        capacity = request.form.get('capacity', type=float)
        capacity_unit = request.form.get('capacity_unit')
        temperature_range = request.form.get('temperature_range')
        humidity_range = request.form.get('humidity_range')
        status = request.form.get('status')
        notes = request.form.get('notes')

        # 验证数据
        if not name or not location or not manager_id:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('warehouse.edit', id=id))

        # 使用原始SQL语句更新仓库信息，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含updated_at字段，让数据库使用默认值
            sql = text("""
            UPDATE warehouses
            SET name = :name, location = :location, manager_id = :manager_id, capacity = :capacity,
                capacity_unit = :capacity_unit, temperature_range = :temperature_range,
                humidity_range = :humidity_range, status = :status, notes = :notes
            WHERE id = :id
            """)

            # 执行SQL语句
            db.session.execute(sql,
                {"name": name, "location": location, "manager_id": manager_id, "capacity": capacity,
                 "capacity_unit": capacity_unit, "temperature_range": temperature_range,
                 "humidity_range": humidity_range, "status": status, "notes": notes, "id": id}
            )

            # 提交事务
            db.session.commit()

            flash('仓库信息更新成功', 'success')
            return redirect(url_for('warehouse.view', id=id))
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新仓库时出错: {str(e)}")
            flash(f'更新仓库时出错: {str(e)}', 'danger')
            return redirect(url_for('warehouse.edit', id=id))

    # GET请求，显示编辑表单
    # 获取可选的管理员列表
    accessible_areas = current_user.get_accessible_areas()
    managers = User.query.filter(User.area_id.in_([area.id for area in accessible_areas])).all()

    return render_template('warehouse/form.html',
                          warehouse=warehouse,
                          areas=None,  # 编辑时不允许修改区域
                          managers=managers,
                          title='编辑仓库')
