"""
照片服务

提供照片管理所需的数据服务，包括照片上传、获取、删除等。
"""

import os
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app
from app import db
from app.models_daily_management import Photo
from app.utils.log_activity import log_activity


class PhotoService:
    """照片服务类"""

    @staticmethod
    def get_photos_by_reference(reference_type, reference_id):
        """
        获取引用对象的照片
        
        Args:
            reference_type: 引用类型（inspection, companion, training, event, issue）
            reference_id: 引用ID
            
        Returns:
            list: 照片列表
        """
        return Photo.query.filter_by(reference_type=reference_type, reference_id=reference_id).all()

    @staticmethod
    def get_photo_by_id(photo_id):
        """
        根据ID获取照片
        
        Args:
            photo_id: 照片ID
            
        Returns:
            Photo: 照片对象
        """
        return Photo.query.get(photo_id)

    @staticmethod
    def save_photo(file, reference_type, reference_id, description=None):
        """
        保存照片
        
        Args:
            file: 上传的文件对象
            reference_type: 引用类型（inspection, companion, training, event, issue）
            reference_id: 引用ID
            description: 照片描述
            
        Returns:
            Photo: 创建的照片对象
        """
        if not file:
            return None
            
        # 确保文件名安全
        filename = secure_filename(file.filename)
        
        # 生成唯一文件名
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_filename = f"{timestamp}_{filename}"
        
        # 确定保存路径
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            reference_type
        )
        
        # 确保目录存在
        os.makedirs(upload_folder, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_folder, unique_filename)
        file.save(file_path)
        
        # 相对路径（用于数据库存储）
        relative_path = os.path.join(
            'uploads',
            'daily_management',
            reference_type,
            unique_filename
        )
        
        # 创建照片记录
        photo = Photo(
            reference_type=reference_type,
            reference_id=reference_id,
            file_name=filename,
            file_path=relative_path,
            description=description,
            upload_time=datetime.now()
        )
        
        db.session.add(photo)
        db.session.commit()
        
        # 记录活动
        log_activity('上传了照片', f'类型: {reference_type}, ID: {reference_id}', 'photo', photo.id)
        
        return photo

    @staticmethod
    def update_photo(photo_id, description):
        """
        更新照片描述
        
        Args:
            photo_id: 照片ID
            description: 照片描述
            
        Returns:
            Photo: 更新后的照片对象
        """
        photo = Photo.query.get(photo_id)
        
        if not photo:
            return None
            
        # 更新描述
        photo.description = description
        
        db.session.commit()
        
        # 记录活动
        log_activity('更新了照片描述', f'照片ID: {photo_id}', 'photo', photo_id)
        
        return photo

    @staticmethod
    def delete_photo(photo_id):
        """
        删除照片
        
        Args:
            photo_id: 照片ID
            
        Returns:
            bool: 是否删除成功
        """
        photo = Photo.query.get(photo_id)
        
        if not photo:
            return False
            
        # 删除文件
        file_path = os.path.join(current_app.static_folder, photo.file_path)
        if os.path.exists(file_path):
            os.remove(file_path)
            
        # 删除记录
        db.session.delete(photo)
        db.session.commit()
        
        # 记录活动
        log_activity('删除了照片', f'照片ID: {photo_id}', 'photo', photo_id)
        
        return True
