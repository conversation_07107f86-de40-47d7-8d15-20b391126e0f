from flask import render_template, redirect, url_for, flash, request, current_app
from flask_login import login_required, current_user
from app import db
from app.employee import employee_bp
from sqlalchemy import text
from app.employee.forms import (
    EmployeeForm, HealthCertificateForm, MedicalExaminationForm,
    DailyHealthCheckForm, TrainingRecordForm
)
from app.models import (
    Employee, HealthCertificate, MedicalExamination,
    DailyHealthCheck, TrainingRecord, AdministrativeArea,
    User, Role
)
from app.utils import area_required, filter_by_user_area
from datetime import datetime, date
import os
from werkzeug.utils import secure_filename

def save_file(file, folder):
    """保存文件并返回文件路径"""
    if not file:
        return None

    filename = secure_filename(file.filename)
    # 生成唯一文件名
    unique_filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{filename}"

    # 确保目录存在
    upload_folder = os.path.join(current_app.config['UPLOAD_FOLDER'], folder)
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)

    # 保存文件
    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)

    # 返回相对路径
    return os.path.join('uploads', folder, unique_filename)

@employee_bp.route('/')
@login_required
def index():
    """员工管理首页"""
    # 获取所有员工，根据用户权限过滤
    page = request.args.get('page', 1, type=int)
    query = Employee.query

    # 根据用户权限过滤员工数据
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_areas = [area.id for area in current_user.get_accessible_areas()]
        query = query.filter(Employee.area_id.in_(accessible_areas))

    # 添加排序，SQL Server要求分页必须有ORDER BY
    query = query.order_by(Employee.id.desc())

    # 获取所有员工（用于仪表盘统计）
    all_employees = query.all()

    # 分页显示员工列表
    employees = query.paginate(
        page=page,
        per_page=current_app.config['ITEMS_PER_PAGE'],
        error_out=0
    )

    # 获取健康证即将到期的员工
    expiring_certs = []
    expired_certs = []
    for employee in all_employees:
        cert = employee.get_latest_health_certificate()
        if cert:
            if cert.expire_date > date.today() and (cert.expire_date - date.today()).days <= 30:
                expiring_certs.append({
                    'employee': employee,
                    'certificate': cert,
                    'days_left': (cert.expire_date - date.today()).days
                })
            elif cert.expire_date < date.today():
                expired_certs.append({
                    'employee': employee,
                    'certificate': cert,
                    'days_expired': (date.today() - cert.expire_date).days
                })

    # 按剩余天数排序
    expiring_certs.sort(key=lambda x: x['days_left'])
    expired_certs.sort(key=lambda x: x['days_expired'], reverse=True)

    # 仪表盘数据
    dashboard_data = {
        'total_employees': len(all_employees),
        'active_employees': sum(1 for e in all_employees if e.status == 1),  # 在职
        'inactive_employees': sum(1 for e in all_employees if e.status == 0),  # 离职
        'on_leave_employees': sum(1 for e in all_employees if e.status == 2),  # 休假
        'with_system_account': sum(1 for e in all_employees if e.user_id is not None),
        'without_system_account': sum(1 for e in all_employees if e.user_id is None),
        'health_cert_valid': sum(1 for e in all_employees if e.get_health_certificate_status() == "有效"),
        'health_cert_expiring': len(expiring_certs),
        'health_cert_expired': len(expired_certs),
        'health_cert_none': sum(1 for e in all_employees if e.get_health_certificate_status() == "未办理"),
        'safety_violations': sum(e.safety_violation_count or 0 for e in all_employees),
        'departments': {},
        'positions': {},
        'areas': {}
    }

    # 部门分布
    for employee in all_employees:
        if employee.department:
            if employee.department in dashboard_data['departments']:
                dashboard_data['departments'][employee.department] += 1
            else:
                dashboard_data['departments'][employee.department] = 1

    # 职位分布
    for employee in all_employees:
        if employee.position:
            if employee.position in dashboard_data['positions']:
                dashboard_data['positions'][employee.position] += 1
            else:
                dashboard_data['positions'][employee.position] = 1

    # 区域分布
    for employee in all_employees:
        if employee.area:
            area_name = employee.area.name
            if area_name in dashboard_data['areas']:
                dashboard_data['areas'][area_name] += 1
            else:
                dashboard_data['areas'][area_name] = 1

    # 排序部门、职位和区域数据（取前5个）
    dashboard_data['departments'] = dict(sorted(dashboard_data['departments'].items(), key=lambda x: x[1], reverse=True)[:5])
    dashboard_data['positions'] = dict(sorted(dashboard_data['positions'].items(), key=lambda x: x[1], reverse=True)[:5])
    dashboard_data['areas'] = dict(sorted(dashboard_data['areas'].items(), key=lambda x: x[1], reverse=True)[:5])

    return render_template(
        'employee/index.html',
        title='员工管理',
        employees=employees,
        expiring_certs=expiring_certs,
        expired_certs=expired_certs,
        dashboard_data=dashboard_data,
        now=datetime.now()
    )

@employee_bp.route('/add', methods=['GET', 'POST'])
@login_required
def add_employee():
    """添加员工"""
    form = EmployeeForm()
    if form.validate_on_submit():
        # 处理照片上传
        photo_path = None
        if form.photo.data:
            photo_path = save_file(form.photo.data, 'employee_photos')

        # 处理食品安全责任字段
        responsible_areas = form.responsible_areas.data
        food_safety_certifications = form.food_safety_certifications.data

        # 使用原始SQL语句创建员工记录，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含created_at和updated_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO employees (
                name, gender, birth_date, phone, address, position, department,
                photo, status, entry_date, leave_date, area_id,
                responsible_areas, food_safety_certifications, safety_violation_count,
                user_id, last_health_check_date, health_status
            )
            OUTPUT inserted.id
            VALUES (
                :name, :gender, :birth_date, :phone, :address, :position, :department,
                :photo, :status, :entry_date, :leave_date, :area_id,
                :responsible_areas, :food_safety_certifications, :safety_violation_count,
                :user_id, :last_health_check_date, :health_status
            )
            """).bindparams(
                name=form.name.data,
                gender=form.gender.data,
                birth_date=form.birth_date.data,
                phone=form.phone.data,
                address=form.address.data,
                position=form.position.data,
                department=form.department.data,
                photo=photo_path,
                status=form.status.data,
                entry_date=form.entry_date.data,
                leave_date=form.leave_date.data,
                area_id=form.area_id.data if form.area_id.data > 0 else None,
                responsible_areas=responsible_areas,
                food_safety_certifications=food_safety_certifications,
                safety_violation_count=0,
                user_id=None,
                last_health_check_date=None,
                health_status='正常'
            )

            # 执行SQL语句
            result = db.session.execute(sql)

            # 获取新创建的员工ID
            employee_id = result.fetchone()[0]

            # 处理系统账号关联
            if form.create_user_account.data:
                # 检查用户名是否已存在
                existing_user = User.query.filter_by(username=form.username.data).first()
                if existing_user:
                    flash(f'用户名 {form.username.data} 已存在，请选择其他用户名', 'danger')
                    db.session.rollback()
                    return render_template('employee/employee_form.html', title='添加员工', form=form, now=datetime.now())

                # 创建用户账号
                user = User(
                    username=form.username.data,
                    real_name=form.name.data,
                    phone=form.phone.data,
                    email=f"{form.username.data}@example.com",  # 默认邮箱
                    area_id=form.area_id.data if form.area_id.data > 0 else None,
                    area_level=None,  # 根据区域自动设置区域级别
                    status=1  # 启用状态
                )

                # 设置区域级别
                if user.area_id:
                    area = AdministrativeArea.query.get(user.area_id)
                    if area:
                        user.area_level = area.level

                user.set_password(form.password.data)
                db.session.add(user)
                db.session.flush()  # 获取user.id

                # 关联员工和用户 - 使用SQL更新
                update_sql = text("""
                UPDATE employees
                SET user_id = :user_id
                WHERE id = :employee_id
                """).bindparams(
                    user_id=user.id,
                    employee_id=employee_id
                )

                db.session.execute(update_sql)

                # 分配角色
                default_role_assigned = False
                if form.roles.data:
                    for role_id in form.roles.data:
                        role = Role.query.get(role_id)
                        if role:
                            user.roles.append(role)
                            default_role_assigned = True

                # 如果没有选择任何角色，根据区域级别分配默认角色
                if not default_role_assigned and user.area_level:
                    default_role_name = None
                    if user.area_level == 4:  # 食堂级别
                        default_role_name = '食堂管理员'
                    elif user.area_level == 3:  # 学校级别
                        default_role_name = '学校管理员'
                    elif user.area_level == 2:  # 乡镇级别
                        default_role_name = '乡镇管理员'
                    elif user.area_level == 1:  # 县市区级别
                        default_role_name = '区域管理员'

                    if default_role_name:
                        default_role = Role.query.filter_by(name=default_role_name).first()
                        if default_role:
                            user.roles.append(default_role)
                            flash(f'已自动分配默认角色: {default_role_name}', 'info')

                flash('系统账号创建成功！', 'success')

            # 提交事务
            db.session.commit()
            flash('员工添加成功', 'success')
            return redirect(url_for('employee.view_employee', id=employee_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建员工时出错: {str(e)}")
            flash(f'创建员工时出错: {str(e)}', 'danger')
            return render_template('employee/employee_form.html', title='添加员工', form=form, now=datetime.now(), current_user=current_user, roles_data=[])

    # 获取所有角色，用于前端显示
    roles = Role.query.all()
    roles_data = [{'id': role.id, 'name': role.name, 'description': role.description} for role in roles]

    return render_template('employee/employee_form.html', title='添加员工', form=form, now=datetime.now(), current_user=current_user, roles_data=roles_data)

@employee_bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_employee(id):
    """编辑员工"""
    employee = Employee.query.get_or_404(id)
    form = EmployeeForm(obj=employee)

    # 如果员工已关联用户，设置相关字段
    if employee.user:
        form.username.data = employee.user.username
        form.roles.data = [role.id for role in employee.user.roles]

    if form.validate_on_submit():
        try:
            # 处理照片上传
            photo_path = employee.photo
            if form.photo.data:
                photo_path = save_file(form.photo.data, 'employee_photos')

            # 构建SQL语句，不包含updated_at字段，让数据库使用默认值
            sql = text("""
            UPDATE employees
            SET name = :name,
                gender = :gender,
                birth_date = :birth_date,
                phone = :phone,
                address = :address,
                position = :position,
                department = :department,
                status = :status,
                entry_date = :entry_date,
                leave_date = :leave_date,
                area_id = :area_id,
                responsible_areas = :responsible_areas,
                food_safety_certifications = :food_safety_certifications,
                photo = :photo
            WHERE id = :id
            """).bindparams(
                name=form.name.data,
                gender=form.gender.data,
                birth_date=form.birth_date.data,
                phone=form.phone.data,
                address=form.address.data,
                position=form.position.data,
                department=form.department.data,
                status=form.status.data,
                entry_date=form.entry_date.data,
                leave_date=form.leave_date.data,
                area_id=form.area_id.data if form.area_id.data > 0 else None,
                responsible_areas=form.responsible_areas.data,
                food_safety_certifications=form.food_safety_certifications.data,
                photo=photo_path,
                id=id
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 处理系统账号关联
            if form.create_user_account.data and not employee.user:
                # 检查用户名是否已存在
                existing_user = User.query.filter_by(username=form.username.data).first()
                if existing_user:
                    flash(f'用户名 {form.username.data} 已存在，请选择其他用户名', 'danger')
                    db.session.rollback()
                    return render_template('employee/employee_form.html', title='编辑员工', form=form, employee=employee, now=datetime.now())

                # 创建用户账号
                user = User(
                    username=form.username.data,
                    real_name=form.name.data,
                    phone=form.phone.data,
                    email=f"{form.username.data}@example.com",  # 默认邮箱
                    area_id=form.area_id.data if form.area_id.data > 0 else None,
                    area_level=None,  # 根据区域自动设置区域级别
                    status=1  # 启用状态
                )

                # 设置区域级别
                if user.area_id:
                    area = AdministrativeArea.query.get(user.area_id)
                    if area:
                        user.area_level = area.level

                user.set_password(form.password.data)
                db.session.add(user)
                db.session.flush()  # 获取user.id

                # 关联员工和用户 - 使用SQL更新
                update_sql = text("""
                UPDATE employees
                SET user_id = :user_id
                WHERE id = :employee_id
                """).bindparams(
                    user_id=user.id,
                    employee_id=id
                )

                db.session.execute(update_sql)

                # 分配角色
                default_role_assigned = False
                if form.roles.data:
                    for role_id in form.roles.data:
                        role = Role.query.get(role_id)
                        if role:
                            user.roles.append(role)
                            default_role_assigned = True

                # 如果没有选择任何角色，根据区域级别分配默认角色
                if not default_role_assigned and user.area_level:
                    default_role_name = None
                    if user.area_level == 4:  # 食堂级别
                        default_role_name = '食堂管理员'
                    elif user.area_level == 3:  # 学校级别
                        default_role_name = '学校管理员'
                    elif user.area_level == 2:  # 乡镇级别
                        default_role_name = '乡镇管理员'
                    elif user.area_level == 1:  # 县市区级别
                        default_role_name = '区域管理员'

                    if default_role_name:
                        default_role = Role.query.filter_by(name=default_role_name).first()
                        if default_role:
                            user.roles.append(default_role)
                            flash(f'已自动分配默认角色: {default_role_name}', 'info')

                flash('系统账号创建成功！', 'success')

            # 如果员工状态变为离职，禁用关联的用户账号
            if form.status.data == 0 and employee.user and employee.user.status == 1:
                # 使用SQL更新用户状态
                user_update_sql = text("""
                UPDATE users
                SET status = 0
                WHERE id = :user_id
                """).bindparams(
                    user_id=employee.user.id
                )

                db.session.execute(user_update_sql)

                flash(f'员工离职，关联的系统账号 {employee.user.username} 已被禁用', 'warning')

            # 提交事务
            db.session.commit()
            flash('员工信息更新成功', 'success')
            return redirect(url_for('employee.view_employee', id=id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新员工时出错: {str(e)}")
            flash(f'更新员工时出错: {str(e)}', 'danger')
            return render_template('employee/employee_form.html', title='编辑员工', form=form, employee=employee, now=datetime.now(), current_user=current_user, roles_data=[])

    # 获取所有角色，用于前端显示
    roles = Role.query.all()
    roles_data = [{'id': role.id, 'name': role.name, 'description': role.description} for role in roles]

    return render_template('employee/employee_form.html', title='编辑员工', form=form, employee=employee, now=datetime.now(), current_user=current_user, roles_data=roles_data)

@employee_bp.route('/view/<int:id>')
@login_required
def view_employee(id):
    """查看员工详情"""
    employee = Employee.query.get_or_404(id)

    # 检查用户是否有权限查看该员工
    if not current_user.is_admin() and employee.area_id and not current_user.can_access_area_by_id(employee.area_id):
        flash('您没有权限查看此员工信息', 'danger')
        return redirect(url_for('employee.index'))

    # 获取健康证信息
    health_certificates = HealthCertificate.query.filter_by(employee_id=id).order_by(HealthCertificate.issue_date.desc()).all()

    # 获取体检记录
    medical_examinations = MedicalExamination.query.filter_by(employee_id=id).order_by(MedicalExamination.exam_date.desc()).all()

    # 获取健康检查记录
    health_checks = DailyHealthCheck.query.filter_by(employee_id=id).order_by(DailyHealthCheck.check_date.desc()).limit(10).all()

    # 获取培训记录
    training_records = TrainingRecord.query.filter_by(employee_id=id).order_by(TrainingRecord.training_date.desc()).all()

    return render_template(
        'employee/view_employee.html',
        title=f'员工详情 - {employee.name}',
        employee=employee,
        health_certificates=health_certificates,
        medical_examinations=medical_examinations,
        health_checks=health_checks,
        training_records=training_records,
        now=datetime.now()
    )

@employee_bp.route('/<int:employee_id>/health-certificate/add', methods=['GET', 'POST'])
@login_required
def add_health_certificate(employee_id):
    """添加健康证"""
    employee = Employee.query.get_or_404(employee_id)
    form = HealthCertificateForm()

    if form.validate_on_submit():
        # 处理证件图片上传
        certificate_img_path = None
        if form.certificate_img.data:
            certificate_img_path = save_file(form.certificate_img.data, 'health_certificates')

        # 使用原始SQL语句创建健康证记录，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含created_at和updated_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO health_certificates (
                employee_id, certificate_no, issue_authority, issue_date,
                expire_date, certificate_img, status, notes
            )
            VALUES (
                :employee_id, :certificate_no, :issue_authority, :issue_date,
                :expire_date, :certificate_img, :status, :notes
            )
            """).bindparams(
                employee_id=employee_id,
                certificate_no=form.certificate_no.data,
                issue_authority=form.issue_authority.data,
                issue_date=form.issue_date.data,
                expire_date=form.expire_date.data,
                certificate_img=certificate_img_path,
                status=form.status.data,
                notes=form.notes.data
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 提交事务
            db.session.commit()

            flash('健康证信息添加成功', 'success')
            return redirect(url_for('employee.view_employee', id=employee_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加健康证时出错: {str(e)}")
            flash(f'添加健康证时出错: {str(e)}', 'danger')
            return render_template(
                'employee/health_certificate_form.html',
                title=f'添加健康证 - {employee.name}',
                form=form,
                employee=employee,
                now=datetime.now()
            )

    return render_template(
        'employee/health_certificate_form.html',
        title=f'添加健康证 - {employee.name}',
        form=form,
        employee=employee,
        now=datetime.now()
    )

@employee_bp.route('/<int:employee_id>/medical-examination/add', methods=['GET', 'POST'])
@login_required
def add_medical_examination(employee_id):
    """添加体检记录"""
    employee = Employee.query.get_or_404(employee_id)
    form = MedicalExaminationForm()

    if form.validate_on_submit():
        # 处理体检报告上传
        report_img_path = None
        if form.report_img.data:
            report_img_path = save_file(form.report_img.data, 'medical_reports')

        # 使用原始SQL语句创建体检记录，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含created_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO medical_examinations (
                employee_id, exam_date, exam_hospital, result,
                report_img, notes
            )
            VALUES (
                :employee_id, :exam_date, :exam_hospital, :result,
                :report_img, :notes
            )
            """).bindparams(
                employee_id=employee_id,
                exam_date=form.exam_date.data,
                exam_hospital=form.exam_hospital.data,
                result=form.result.data,
                report_img=report_img_path,
                notes=form.notes.data
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 提交事务
            db.session.commit()

            flash('体检记录添加成功', 'success')
            return redirect(url_for('employee.view_employee', id=employee_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加体检记录时出错: {str(e)}")
            flash(f'添加体检记录时出错: {str(e)}', 'danger')
            return render_template(
                'employee/medical_examination_form.html',
                title=f'添加体检记录 - {employee.name}',
                form=form,
                employee=employee,
                now=datetime.now()
            )

    return render_template(
        'employee/medical_examination_form.html',
        title=f'添加体检记录 - {employee.name}',
        form=form,
        employee=employee,
        now=datetime.now()
    )

@employee_bp.route('/<int:employee_id>/health-check/add', methods=['GET', 'POST'])
@login_required
def add_health_check(employee_id):
    """添加健康检查记录"""
    employee = Employee.query.get_or_404(employee_id)
    form = DailyHealthCheckForm()

    if form.validate_on_submit():
        # 使用原始SQL语句创建健康检查记录，避免ORM处理datetime字段
        try:
            # 设置follow_up_required值
            follow_up_required = form.follow_up_required.data if hasattr(form, 'follow_up_required') else False

            # 如果健康状态异常，设置需要跟进
            if form.health_status.data != '正常':
                follow_up_required = True
                flash(f'员工健康状态异常，已标记为需要跟进', 'warning')

            # 构建SQL语句，不包含created_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO daily_health_checks (
                employee_id, check_date, temperature, health_status,
                symptoms, checker_id, notes, follow_up_required
            )
            VALUES (
                :employee_id, :check_date, :temperature, :health_status,
                :symptoms, :checker_id, :notes, :follow_up_required
            )
            """).bindparams(
                employee_id=employee_id,
                check_date=form.check_date.data,
                temperature=form.temperature.data,
                health_status=form.health_status.data,
                symptoms=form.symptoms.data,
                checker_id=current_user.id,
                notes=form.notes.data,
                follow_up_required=follow_up_required
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 更新员工的最近健康检查日期和健康状态
            update_sql = text("""
            UPDATE employees
            SET last_health_check_date = :check_date,
                health_status = :health_status
            WHERE id = :employee_id
            """).bindparams(
                check_date=form.check_date.data,
                health_status=form.health_status.data,
                employee_id=employee_id
            )

            db.session.execute(update_sql)

            # 提交事务
            db.session.commit()

            flash('健康检查记录添加成功', 'success')
            return redirect(url_for('employee.view_employee', id=employee_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加健康检查记录时出错: {str(e)}")
            flash(f'添加健康检查记录时出错: {str(e)}', 'danger')
            return render_template(
                'employee/health_check_form.html',
                title=f'添加健康检查记录 - {employee.name}',
                form=form,
                employee=employee,
                now=datetime.now()
            )

    return render_template(
        'employee/health_check_form.html',
        title=f'添加健康检查记录 - {employee.name}',
        form=form,
        employee=employee,
        now=datetime.now()
    )

@employee_bp.route('/<int:employee_id>/training-record/add', methods=['GET', 'POST'])
@login_required
def add_training_record(employee_id):
    """添加培训记录"""
    employee = Employee.query.get_or_404(employee_id)
    form = TrainingRecordForm()

    if form.validate_on_submit():
        # 处理证书图片上传
        certificate_img_path = None
        if form.certificate_img.data:
            certificate_img_path = save_file(form.certificate_img.data, 'training_certificates')

        # 使用原始SQL语句创建培训记录，避免ORM处理datetime字段
        try:
            # 构建SQL语句，不包含created_at字段，让数据库使用默认值
            sql = text("""
            INSERT INTO training_records (
                employee_id, training_name, training_date, expire_date,
                certificate_no, certificate_img, score, trainer, notes
            )
            VALUES (
                :employee_id, :training_name, :training_date, :expire_date,
                :certificate_no, :certificate_img, :score, :trainer, :notes
            )
            """).bindparams(
                employee_id=employee_id,
                training_name=form.training_name.data,
                training_date=form.training_date.data,
                expire_date=form.expire_date.data,
                certificate_no=form.certificate_no.data,
                certificate_img=certificate_img_path,
                score=form.score.data,
                trainer=form.trainer.data,
                notes=form.notes.data
            )

            # 执行SQL语句
            db.session.execute(sql)

            # 提交事务
            db.session.commit()

            flash('培训记录添加成功', 'success')
            return redirect(url_for('employee.view_employee', id=employee_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"添加培训记录时出错: {str(e)}")
            flash(f'添加培训记录时出错: {str(e)}', 'danger')
            return render_template(
                'employee/training_record_form.html',
                title=f'添加培训记录 - {employee.name}',
                form=form,
                employee=employee,
                now=datetime.now()
            )

    return render_template(
        'employee/training_record_form.html',
        title=f'添加培训记录 - {employee.name}',
        form=form,
        employee=employee,
        now=datetime.now()
    )

@employee_bp.route('/health-certificates')
@login_required
def health_certificates():
    """健康证管理"""
    # 获取所有员工的健康证状态，根据用户权限过滤
    query = Employee.query

    # 根据用户权限过滤员工数据
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_areas = [area.id for area in current_user.get_accessible_areas()]
        query = query.filter(Employee.area_id.in_(accessible_areas))

    employees = query.all()
    employee_certs = []

    for employee in employees:
        cert = employee.get_latest_health_certificate()
        status = employee.get_health_certificate_status()

        employee_certs.append({
            'employee': employee,
            'certificate': cert,
            'status': status,
            'days_left': (cert.expire_date - date.today()).days if cert and cert.expire_date > date.today() else 0
        })

    # 按状态排序：已过期 > 即将过期 > 有效 > 未办理
    def sort_key(item):
        status = item['status']
        if status == "已过期":
            return 0
        elif "即将过期" in status:
            return 1
        elif status == "有效":
            return 2
        else:  # 未办理
            return 3

    employee_certs.sort(key=sort_key)

    return render_template(
        'employee/health_certificates.html',
        title='健康证管理',
        employee_certs=employee_certs,
        now=datetime.now()
    )

@employee_bp.route('/daily-health-check')
@login_required
def daily_health_check():
    """日常健康检查"""
    # 获取所有在职员工，根据用户权限过滤
    query = Employee.query.filter_by(status=1)

    # 根据用户权限过滤员工数据
    if not current_user.is_admin():
        # 获取用户可访问的区域ID列表
        accessible_areas = [area.id for area in current_user.get_accessible_areas()]
        query = query.filter(Employee.area_id.in_(accessible_areas))

    employees = query.all()

    # 获取今日已检查的员工ID列表
    today = date.today()
    checked_employee_ids = [
        check.employee_id for check in
        DailyHealthCheck.query.filter(DailyHealthCheck.check_date == today).all()
    ]

    # 分类员工：已检查和未检查
    checked_employees = []
    unchecked_employees = []

    for employee in employees:
        if employee.id in checked_employee_ids:
            # 获取今日检查记录
            check = DailyHealthCheck.query.filter_by(
                employee_id=employee.id,
                check_date=today
            ).first()
            checked_employees.append({
                'employee': employee,
                'check': check
            })
        else:
            unchecked_employees.append(employee)

    return render_template(
        'employee/daily_health_check.html',
        title='日常健康检查',
        checked_employees=checked_employees,
        unchecked_employees=unchecked_employees,
        today=today,
        now=datetime.now()
    )
