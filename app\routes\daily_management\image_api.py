"""
图片上传与管理API
提供独立的图片上传、评分和管理功能
"""

import os
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, render_template
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from PIL import Image
from app import db
from app.models_daily_management import Photo, InspectionRecord
from sqlalchemy import text

image_api_bp = Blueprint('image_api', __name__)

@image_api_bp.route('/upload', methods=['POST'])
@login_required
def upload_image():
    """上传图片API
    参数:
        reference_type: 引用类型 (inspection, companion, training, event, issue)
        reference_id: 引用ID
        photo: 图片文件
    返回:
        JSON格式的上传结果
    """
    if 'photo' not in request.files:
        return jsonify({'error': '没有文件'}), 400

    photo_file = request.files['photo']
    if not photo_file.filename:
        return jsonify({'error': '没有选择文件'}), 400

    reference_type = request.form.get('reference_type')
    reference_id = request.form.get('reference_id')

    if not reference_type or not reference_id:
        return jsonify({'error': '缺少引用类型或ID'}), 400

    try:
        # 确保文件名安全
        filename = secure_filename(photo_file.filename)

        # 生成唯一文件名
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_filename = f"{timestamp}_{filename}"

        # 确定保存路径
        upload_folder = os.path.join(
            current_app.static_folder,
            'uploads',
            'daily_management',
            reference_type
        )

        # 确保目录存在
        os.makedirs(upload_folder, exist_ok=True)

        # 直接保存原始图片，不进行处理
        file_path = os.path.join(upload_folder, unique_filename)
        photo_file.save(file_path)

        # 相对路径（用于数据库存储）
        relative_path = f"/static/uploads/daily_management/{reference_type}/{unique_filename}"

        # 使用原始SQL创建照片记录，避免ORM问题
        sql = text("""
        INSERT INTO photos
        (reference_id, reference_type, file_name, file_path, rating, upload_time)
        OUTPUT inserted.id
        VALUES (:reference_id, :reference_type, :file_name, :file_path, :rating, :upload_time)
        """)

        params = {
            'reference_id': reference_id,
            'reference_type': reference_type,
            'file_name': unique_filename,
            'file_path': relative_path,
            'rating': 3,  # 默认评分为3星
            'upload_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 执行SQL
        result = db.session.execute(sql, params)
        photo_id = result.fetchone()[0]
        db.session.commit()

        # 记录日志
        current_app.logger.info(f'照片上传成功: ID={photo_id}, 路径={relative_path}')

        return jsonify({
            'success': True,
            'id': photo_id,
            'file_path': relative_path,
            'rating': 3,
            'message': '照片上传成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'照片上传失败: {str(e)}')
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@image_api_bp.route('/update-rating', methods=['POST'])
@login_required
def update_rating():
    """更新图片评分API
    参数:
        photo_id: 照片ID
        rating: 评分 (1-5)
    返回:
        JSON格式的更新结果
    """
    data = request.json
    if not data or 'photo_id' not in data or 'rating' not in data:
        return jsonify({'error': '缺少必要参数'}), 400

    photo_id = data['photo_id']
    rating = data['rating']

    if not isinstance(rating, int) or rating < 1 or rating > 5:
        return jsonify({'error': '评分必须是1-5之间的整数'}), 400

    try:
        # 使用原始SQL更新评分，避免ORM问题
        sql = text("""
        UPDATE photos
        SET rating = :rating
        WHERE id = :id
        """)

        db.session.execute(sql, {'rating': rating, 'id': photo_id})
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '评分更新成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新失败: {str(e)}'}), 500

@image_api_bp.route('/delete', methods=['POST'])
@login_required
def delete_image():
    """删除图片API
    参数:
        photo_id: 照片ID
    返回:
        JSON格式的删除结果
    """
    data = request.json
    if not data or 'photo_id' not in data:
        return jsonify({'error': '缺少照片ID'}), 400

    photo_id = data['photo_id']

    try:
        # 先获取照片信息
        sql = text("""
        SELECT file_path FROM photos WHERE id = :id
        """)
        result = db.session.execute(sql, {'id': photo_id})
        photo_info = result.fetchone()

        if not photo_info:
            return jsonify({'error': '照片不存在'}), 404

        file_path = photo_info[0]

        # 删除物理文件
        full_path = os.path.join(current_app.static_folder, file_path.lstrip('/static/'))
        if os.path.exists(full_path):
            os.remove(full_path)

        # 删除数据库记录
        sql = text("""
        DELETE FROM photos WHERE id = :id
        """)
        db.session.execute(sql, {'id': photo_id})
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '照片删除成功'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除失败: {str(e)}'}), 500

@image_api_bp.route('/list', methods=['GET'])
@login_required
def list_images():
    """获取图片列表API
    参数:
        reference_type: 引用类型
        reference_id: 引用ID
    返回:
        JSON格式的图片列表
    """
    reference_type = request.args.get('reference_type')
    reference_id = request.args.get('reference_id')

    if not reference_type or not reference_id:
        return jsonify({'error': '缺少引用类型或ID'}), 400

    try:
        # 使用原始SQL查询，避免ORM问题
        sql = text("""
        SELECT id, file_path, rating, upload_time
        FROM photos
        WHERE reference_type = :reference_type AND reference_id = :reference_id
        ORDER BY upload_time DESC
        """)

        result = db.session.execute(sql, {
            'reference_type': reference_type,
            'reference_id': reference_id
        })

        photos = []
        for row in result:
            photos.append({
                'id': row[0],
                'file_path': row[1],
                'rating': row[2],
                'upload_time': row[3].strftime('%Y-%m-%d %H:%M:%S') if row[3] else None
            })

        return jsonify({
            'success': True,
            'photos': photos
        })
    except Exception as e:
        return jsonify({'error': f'获取图片列表失败: {str(e)}'}), 500

@image_api_bp.route('/widget', methods=['GET'])
@login_required
def image_widget():
    """图片上传小部件
    参数:
        reference_type: 引用类型
        reference_id: 引用ID
        title: 标题 (可选)
    返回:
        渲染后的小部件HTML
    """
    reference_type = request.args.get('reference_type')
    reference_id = request.args.get('reference_id')
    title = request.args.get('title', '照片上传')

    if not reference_type or not reference_id:
        return "缺少必要参数", 400

    return render_template('daily_management/widgets/image_widget.html',
                          reference_type=reference_type,
                          reference_id=reference_id,
                          title=title)
