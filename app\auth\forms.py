from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models import User, AdministrativeArea
import re
import random
import string

class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(message='请输入用户名')])
    password = PasswordField('密码', validators=[DataRequired(message='请输入密码')])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class RegisterForm(FlaskForm):
    school_name = StringField('学校全称', validators=[
        DataRequired(message='请输入学校全称'),
        Length(min=6, max=50, message='学校全称不能少于6个汉字，请按"县市区+乡镇+学校名"格式输入')
    ])
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=20, message='用户名长度必须在3-20个字符之间')
    ])
    email = StringField('电子邮箱', validators=[
        DataRequired(message='请输入电子邮箱'),
        Email(message='请输入有效的电子邮箱地址')
    ])
    real_name = StringField('真实姓名', validators=[
        DataRequired(message='请输入真实姓名'),
        Length(min=2, max=20, message='姓名长度必须在2-20个字符之间')
    ])
    phone = StringField('手机号码', validators=[
        DataRequired(message='请输入手机号码'),
        Length(min=11, max=11, message='请输入11位手机号码')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码'),
        Length(min=6, message='密码长度不能少于6个字符')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(message='请再次输入密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('创建学校并注册')

    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('该用户名已被使用，请更换一个')

    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('该邮箱已被注册，请更换一个')

    def validate_school_name(self, school_name):
        """验证学校名称格式和唯一性"""
        name = school_name.data.strip()

        # 检查长度（中文字符）
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', name)
        if len(chinese_chars) < 6:
            raise ValidationError('学校全称不能少于6个汉字，请按"县市区+乡镇+学校名"格式输入，如：海淀区中关村第一小学')

        # 检查是否包含常见的地区关键词
        region_keywords = ['市', '区', '县', '镇', '乡', '街道', '村', '社区']
        has_region = any(keyword in name for keyword in region_keywords)
        if not has_region:
            raise ValidationError('请输入完整的学校全称，包含地区信息，如：海淀区中关村第一小学')

        # 检查唯一性
        school = AdministrativeArea.query.filter_by(name=name, level=3).first()
        if school is not None:
            raise ValidationError('该学校名称已被使用，请尝试更详细的地区信息或学校全称')

    @staticmethod
    def generate_username_suggestion(school_name):
        """根据学校名称生成用户名建议"""
        # 简单的拼音首字母映射（常用字）
        pinyin_map = {
            '北': 'B', '京': 'J', '上': 'S', '海': 'H', '天': 'T', '津': 'J',
            '重': 'C', '庆': 'Q', '河': 'H', '北': 'B', '山': 'S', '西': 'X',
            '内': 'N', '蒙': 'M', '古': 'G', '辽': 'L', '宁': 'N', '吉': 'J',
            '林': 'L', '黑': 'H', '龙': 'L', '江': 'J', '苏': 'S', '浙': 'Z',
            '安': 'A', '徽': 'H', '福': 'F', '建': 'J', '西': 'X', '东': 'D',
            '南': 'N', '湖': 'H', '广': 'G', '四': 'S', '川': 'C', '贵': 'G',
            '州': 'Z', '云': 'Y', '陕': 'S', '甘': 'G', '肃': 'S', '青': 'Q',
            '宁': 'N', '夏': 'X', '新': 'X', '疆': 'J', '台': 'T', '湾': 'W',
            '香': 'X', '港': 'G', '澳': 'A', '门': 'M', '市': 'S', '区': 'Q',
            '县': 'X', '镇': 'Z', '乡': 'X', '村': 'C', '学': 'X', '校': 'X',
            '小': 'X', '中': 'Z', '大': 'D', '第': 'D', '一': 'Y', '二': 'E',
            '三': 'S', '四': 'S', '五': 'W', '六': 'L', '七': 'Q', '八': 'B',
            '九': 'J', '十': 'S', '实': 'S', '验': 'Y', '民': 'M', '族': 'Z',
            '外': 'W', '国': 'G', '语': 'Y', '职': 'Z', '业': 'Y', '技': 'J',
            '术': 'S', '师': 'S', '范': 'F', '幼': 'Y', '儿': 'E', '园': 'Y'
        }

        # 提取拼音首字母
        initials = ''
        for char in school_name:
            if char in pinyin_map:
                initials += pinyin_map[char]
            elif char.isalpha():
                initials += char.upper()

        # 限制长度并添加随机数字
        if len(initials) > 8:
            initials = initials[:8]
        elif len(initials) < 4:
            initials = initials + 'XXXX'
            initials = initials[:4]

        # 添加随机数字
        random_num = ''.join(random.choices(string.digits, k=3))
        suggested_username = initials + random_num

        return suggested_username.lower()
