from app import db
from datetime import datetime
from sqlalchemy.orm import relationship

class PurchaseOrder(db.Model):
    """采购订单模型"""
    __tablename__ = 'purchase_orders'

    id = db.Column(db.Integer, primary_key=True)
    order_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'))
    total_amount = db.Column(db.Numeric(10, 2), default=0)
    order_date = db.Column(db.DateTime, default=datetime.now)
    status = db.Column(db.String(20), default='pending')
    delivery_date = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.now)
    requisition_id = db.Column(db.Integer)
    area_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('administrative_areas.id'))
    expected_delivery_date = db.Column(db.Date)
    payment_terms = db.Column(db.String(200))
    delivery_terms = db.Column(db.String(200))
    approved_by = db.Column(db.Integer)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    notes = db.Column(db.Text)
    confirmed_at = db.Column(db.DateTime)
    delivered_at = db.Column(db.DateTime)
    cancelled_at = db.Column(db.DateTime)
    cancel_reason = db.Column(db.Text)
    delivery_notes = db.Column(db.Text)

    # 关系定义
    supplier = relationship('Supplier', backref='purchase_orders')
    area = relationship('AdministrativeArea', backref='purchase_orders')
    order_items = relationship('PurchaseOrderItem', backref='order', lazy='select')

    def get_status_display(self):
        """获取状态显示文本"""
        status_map = {
            'pending': '待确认',
            'confirmed': '已确认',
            'delivered': '已送达',
            'cancelled': '已取消'
        }
        return status_map.get(self.status, self.status)

class PurchaseOrderItem(db.Model):
    """采购订单明细模型"""
    __tablename__ = 'purchase_order_items'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('supplier_products.id'))
    quantity = db.Column(db.Numeric(10, 2), nullable=False)
    unit = db.Column(db.String(20), nullable=False)
    unit_price = db.Column(db.Numeric(10, 2), default=0)
    total_price = db.Column(db.Numeric(10, 2), default=0)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    notes = db.Column(db.Text)

    # 关系定义
    ingredient = relationship('Ingredient', backref='order_items')
    product = relationship('SupplierProduct', backref='order_items') 