from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Warehouse, StorageLocation
from app import db
from datetime import datetime
import json
from sqlalchemy import text

storage_location_bp = Blueprint('storage_location', __name__)

@storage_location_bp.route('/warehouse/<int:warehouse_id>/storage-location/create', methods=['GET', 'POST'])
@login_required
def create(warehouse_id):
    """创建存储位置"""
    warehouse = Warehouse.query.get_or_404(warehouse_id)

    # 检查用户是否有权限操作该仓库
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限操作该仓库', 'danger')
        return redirect(url_for('warehouse.view', id=warehouse_id))

    if request.method == 'POST':
        # 获取表单数据
        name = request.form.get('name')
        location_code = request.form.get('location_code')
        storage_type = request.form.get('storage_type')
        capacity = request.form.get('capacity', type=float)
        capacity_unit = request.form.get('capacity_unit')
        temperature_range = request.form.get('temperature_range')
        status = request.form.get('status')
        notes = request.form.get('notes')

        # 验证数据
        if not name or not location_code or not storage_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('storage_location.create', warehouse_id=warehouse_id))

        # 检查位置编码是否已存在
        existing_location = StorageLocation.query.filter_by(warehouse_id=warehouse_id, location_code=location_code).first()
        if existing_location:
            flash('该位置编码已存在，请使用其他编码', 'warning')
            return redirect(url_for('storage_location.create', warehouse_id=warehouse_id))

        # 使用原始SQL语句创建存储位置，避免ORM处理datetime字段
        try:
            # 构建SQL语句，包含created_at和updated_at字段，使用GETDATE()函数
            sql = text("""
            INSERT INTO storage_locations (warehouse_id, name, location_code, storage_type, capacity,
                                         capacity_unit, temperature_range, status, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES (:warehouse_id, :name, :location_code, :storage_type, :capacity,
                   :capacity_unit, :temperature_range, :status, :notes, GETDATE(), GETDATE())
            """)

            # 执行SQL语句
            result = db.session.execute(sql,
                {"warehouse_id": warehouse_id, "name": name, "location_code": location_code,
                 "storage_type": storage_type, "capacity": capacity, "capacity_unit": capacity_unit,
                 "temperature_range": temperature_range, "status": status, "notes": notes}
            )

            # 获取新创建的存储位置ID
            storage_location_id = result.fetchone()[0]

            # 提交事务
            db.session.commit()

            flash('存储位置创建成功', 'success')
            return redirect(url_for('warehouse.view', id=warehouse_id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建存储位置时出错: {str(e)}")
            flash(f'创建存储位置时出错: {str(e)}', 'danger')
            return redirect(url_for('storage_location.create', warehouse_id=warehouse_id))

    # GET请求，显示创建表单
    return render_template('storage_location/form.html',
                          warehouse=warehouse,
                          storage_location=None,
                          title='创建存储位置')

@storage_location_bp.route('/storage-location/<int:id>')
@login_required
def view(id):
    """查看存储位置详情"""
    storage_location = StorageLocation.query.get_or_404(id)
    warehouse = Warehouse.query.get(storage_location.warehouse_id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限查看该存储位置', 'danger')
        return redirect(url_for('warehouse.index'))

    # 获取该存储位置的库存列表
    inventories = storage_location.inventories.all()

    return render_template('storage_location/view.html',
                          storage_location=storage_location,
                          warehouse=warehouse,
                          inventories=inventories)

@storage_location_bp.route('/storage-location/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """编辑存储位置"""
    storage_location = StorageLocation.query.get_or_404(id)
    warehouse = Warehouse.query.get(storage_location.warehouse_id)

    # 检查用户是否有权限编辑
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限编辑该存储位置', 'danger')
        return redirect(url_for('warehouse.index'))

    if request.method == 'POST':
        # 获取表单数据
        name = request.form.get('name')
        location_code = request.form.get('location_code')
        storage_type = request.form.get('storage_type')
        capacity = request.form.get('capacity', type=float)
        capacity_unit = request.form.get('capacity_unit')
        temperature_range = request.form.get('temperature_range')
        status = request.form.get('status')
        notes = request.form.get('notes')

        # 验证数据
        if not name or not location_code or not storage_type:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('storage_location.edit', id=id))

        # 检查位置编码是否已存在（排除自身）
        existing_location = StorageLocation.query.filter(
            StorageLocation.warehouse_id == storage_location.warehouse_id,
            StorageLocation.location_code == location_code,
            StorageLocation.id != id
        ).first()
        if existing_location:
            flash('该位置编码已存在，请使用其他编码', 'warning')
            return redirect(url_for('storage_location.edit', id=id))

        # 使用原始SQL语句更新存储位置信息，避免ORM处理datetime字段
        try:
            # 构建SQL语句，包含updated_at字段，使用GETDATE()函数
            sql = text("""
            UPDATE storage_locations
            SET name = :name, location_code = :location_code, storage_type = :storage_type,
                capacity = :capacity, capacity_unit = :capacity_unit, temperature_range = :temperature_range,
                status = :status, notes = :notes, updated_at = GETDATE()
            WHERE id = :id
            """)

            # 执行SQL语句
            db.session.execute(sql,
                {"name": name, "location_code": location_code, "storage_type": storage_type,
                 "capacity": capacity, "capacity_unit": capacity_unit, "temperature_range": temperature_range,
                 "status": status, "notes": notes, "id": id}
            )

            # 提交事务
            db.session.commit()

            flash('存储位置信息更新成功', 'success')
            return redirect(url_for('storage_location.view', id=id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新存储位置信息时出错: {str(e)}")
            flash(f'更新存储位置信息时出错: {str(e)}', 'danger')
            return redirect(url_for('storage_location.edit', id=id))

    # GET请求，显示编辑表单
    return render_template('storage_location/form.html',
                          warehouse=warehouse,
                          storage_location=storage_location,
                          title='编辑存储位置')

@storage_location_bp.route('/storage-location/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """删除存储位置"""
    storage_location = StorageLocation.query.get_or_404(id)
    warehouse = Warehouse.query.get(storage_location.warehouse_id)

    # 检查用户是否有权限删除
    if not current_user.can_access_area_by_id(warehouse.area_id):
        flash('您没有权限删除该存储位置', 'danger')
        return redirect(url_for('warehouse.index'))

    # 检查该存储位置是否有关联的库存
    if storage_location.inventories.count() > 0:
        flash('该存储位置有关联的库存记录，无法删除', 'danger')
        return redirect(url_for('storage_location.view', id=id))

    # 删除存储位置
    warehouse_id = storage_location.warehouse_id
    db.session.delete(storage_location)
    db.session.commit()

    flash('存储位置删除成功', 'success')
    return redirect(url_for('warehouse.view', id=warehouse_id))
