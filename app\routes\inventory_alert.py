from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import InventoryAlert, Ingredient, AdministrativeArea, PurchaseRequisition, PurchaseRequisitionItem, Inventory, Warehouse
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func
import json
import uuid

inventory_alert_bp = Blueprint('inventory_alert', __name__)

@inventory_alert_bp.route('/inventory-alert')
@login_required
def index():
    """库存预警列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    area_id = request.args.get('area_id', type=int)
    status = request.args.get('status', '')
    alert_type = request.args.get('alert_type', '')

    # 构建查询
    query = InventoryAlert.query.filter(InventoryAlert.area_id.in_(area_ids))

    # 应用过滤条件
    if area_id:
        query = query.filter_by(area_id=area_id)
    if status:
        query = query.filter_by(status=status)
    if alert_type:
        query = query.filter_by(alert_type=alert_type)

    # 按创建时间降序排序
    query = query.order_by(InventoryAlert.created_at.desc())

    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=0)
    inventory_alerts = pagination.items

    # 获取区域信息，用于过滤
    areas = AdministrativeArea.query.filter(AdministrativeArea.id.in_(area_ids)).all()

    return render_template('inventory_alert/index.html',
                          inventory_alerts=inventory_alerts,
                          pagination=pagination,
                          areas=areas,
                          area_id=area_id,
                          status=status,
                          alert_type=alert_type)

@inventory_alert_bp.route('/inventory-alert/<int:id>')
@login_required
def view(id):
    """查看库存预警详情"""
    inventory_alert = InventoryAlert.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(inventory_alert.area_id):
        flash('您没有权限查看该库存预警', 'danger')
        return redirect(url_for('inventory_alert.index'))

    return render_template('inventory_alert/view.html', inventory_alert=inventory_alert)

@inventory_alert_bp.route('/inventory-alert/<int:id>/process', methods=['POST'])
@login_required
def process(id):
    """处理库存预警"""
    inventory_alert = InventoryAlert.query.get_or_404(id)

    # 检查用户是否有权限处理
    if not current_user.can_access_area_by_id(inventory_alert.area_id):
        flash('您没有权限处理该库存预警', 'danger')
        return redirect(url_for('inventory_alert.index'))

    # 只有未处理状态的预警可以处理
    if inventory_alert.status != '未处理':
        flash('只有未处理状态的预警可以处理', 'danger')
        return redirect(url_for('inventory_alert.view', id=id))

    # 更新状态
    inventory_alert.status = '已处理'
    inventory_alert.processed_by = current_user.id
    inventory_alert.processed_at = datetime.now()
    inventory_alert.notes = request.form.get('notes')

    db.session.commit()
    flash('库存预警已处理', 'success')
    return redirect(url_for('inventory_alert.view', id=id))

@inventory_alert_bp.route('/inventory-alert/<int:id>/create-requisition', methods=['GET', 'POST'])
@login_required
def create_requisition(id):
    """根据库存预警创建采购申请"""
    inventory_alert = InventoryAlert.query.get_or_404(id)

    # 检查用户是否有权限操作
    if not current_user.can_access_area_by_id(inventory_alert.area_id):
        flash('您没有权限操作该库存预警', 'danger')
        return redirect(url_for('inventory_alert.index'))

    # 只有未处理状态的预警可以创建采购申请
    if inventory_alert.status != '未处理':
        flash('只有未处理状态的预警可以创建采购申请', 'danger')
        return redirect(url_for('inventory_alert.view', id=id))

    if request.method == 'POST':
        # 获取表单数据
        quantity = request.form.get('quantity', type=float)
        required_date = request.form.get('required_date')
        notes = request.form.get('notes')

        # 验证数据
        if not quantity or not required_date:
            flash('请填写所有必填字段', 'danger')
            return redirect(url_for('inventory_alert.create_requisition', id=id))

        # 生成采购申请编号
        requisition_number = f"CG{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 创建采购申请
        requisition = PurchaseRequisition(
            requisition_number=requisition_number,
            area_id=inventory_alert.area_id,
            requisition_date=date.today(),
            required_date=datetime.strptime(required_date, '%Y-%m-%d').date(),
            status='待审核',
            created_by=current_user.id,
            notes=notes
        )

        db.session.add(requisition)
        db.session.flush()  # 获取ID

        # 创建采购申请明细
        requisition_item = PurchaseRequisitionItem(
            requisition_id=requisition.id,
            ingredient_id=inventory_alert.ingredient_id,
            quantity=quantity,
            unit=inventory_alert.unit,
            notes=f'由库存预警自动生成'
        )

        db.session.add(requisition_item)

        # 更新预警状态
        inventory_alert.status = '已处理'
        inventory_alert.processed_by = current_user.id
        inventory_alert.processed_at = datetime.now()
        inventory_alert.notes = f'已创建采购申请：{requisition_number}'

        db.session.commit()
        flash('采购申请创建成功', 'success')
        return redirect(url_for('purchase_requisition.view', id=requisition.id))

    # GET请求，显示创建表单
    ingredient = Ingredient.query.get(inventory_alert.ingredient_id)

    return render_template('inventory_alert/create_requisition.html',
                          inventory_alert=inventory_alert,
                          ingredient=ingredient)

@inventory_alert_bp.route('/inventory-alert/batch-process', methods=['POST'])
@login_required
def batch_process():
    """批量处理库存预警"""
    alert_ids = request.form.getlist('alert_ids[]', type=int)

    if not alert_ids:
        flash('请选择要处理的预警', 'warning')
        return redirect(url_for('inventory_alert.index'))

    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取所有选中的预警
    alerts = InventoryAlert.query.filter(
        InventoryAlert.id.in_(alert_ids),
        InventoryAlert.area_id.in_(area_ids),
        InventoryAlert.status == '未处理'
    ).all()

    # 更新状态
    for alert in alerts:
        alert.status = '已处理'
        alert.processed_by = current_user.id
        alert.processed_at = datetime.now()
        alert.notes = '批量处理'

    db.session.commit()
    flash(f'成功处理 {len(alerts)} 条库存预警', 'success')
    return redirect(url_for('inventory_alert.index'))

@inventory_alert_bp.route('/inventory-alert/check')
@login_required
def check_alerts():
    """检查库存预警"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)

    # 应用区域过滤
    if area_id:
        area_ids = [area_id]

    # 存储预警结果
    alerts_result = {
        'low_stock': [],  # 库存不足预警
        'expiring': [],   # 临期预警
        'expired': []     # 过期预警
    }

    # 1. 检查库存不足预警
    # 获取所有库存不足预警设置
    low_stock_alerts = InventoryAlert.query.filter(
        InventoryAlert.area_id.in_(area_ids),
        InventoryAlert.alert_type == '库存不足'
    ).all()

    for alert in low_stock_alerts:
        # 计算该食材在该区域的总库存
        total_stock = db.session.query(func.sum(Inventory.quantity)).filter(
            Inventory.ingredient_id == alert.ingredient_id,
            Inventory.warehouse.has(area_id=alert.area_id),
            Inventory.status == '正常'
        ).scalar() or 0

        # 检查是否低于预警阈值
        if total_stock < alert.min_quantity:
            alerts_result['low_stock'].append({
                'alert': alert,
                'current_stock': total_stock,
                'shortage': alert.min_quantity - total_stock
            })

    # 2. 检查临期预警
    # 获取所有临期预警设置
    expiry_alerts = InventoryAlert.query.filter(
        InventoryAlert.area_id.in_(area_ids),
        InventoryAlert.alert_type == '临期预警'
    ).all()

    for alert in expiry_alerts:
        # 计算预警日期
        alert_date = date.today() + timedelta(days=alert.expiry_days)

        # 查询临期库存
        expiring_items = Inventory.query.filter(
            Inventory.ingredient_id == alert.ingredient_id,
            Inventory.warehouse.has(area_id=alert.area_id),
            Inventory.status == '正常',
            Inventory.quantity > 0,
            Inventory.expiry_date <= alert_date,
            Inventory.expiry_date > date.today()
        ).all()

        if expiring_items:
            alerts_result['expiring'].append({
                'alert': alert,
                'items': expiring_items,
                'days': alert.expiry_days
            })

    # 3. 检查过期预警
    # 查询所有过期库存
    expired_items = Inventory.query.filter(
        Inventory.warehouse.has(Warehouse.area_id.in_(area_ids)),
        Inventory.status == '正常',
        Inventory.quantity > 0,
        Inventory.expiry_date < date.today()
    ).all()

    if expired_items:
        # 按食材分组
        expired_by_ingredient = {}
        for item in expired_items:
            if item.ingredient_id not in expired_by_ingredient:
                expired_by_ingredient[item.ingredient_id] = []
            expired_by_ingredient[item.ingredient_id].append(item)

        for ingredient_id, items in expired_by_ingredient.items():
            alerts_result['expired'].append({
                'ingredient': items[0].ingredient,
                'items': items
            })

    # 获取区域列表
    areas = accessible_areas

    return render_template('inventory_alert/check.html',
                          alerts_result=alerts_result,
                          areas=areas,
                          area_id=area_id)

@inventory_alert_bp.route('/inventory-alert/batch-create-requisition', methods=['GET', 'POST'])
@login_required
def batch_create_requisition():
    """批量创建采购申请"""
    if request.method == 'GET':
        # 处理GET请求，显示表单
        alert_ids = request.args.getlist('alert_ids[]', type=int)

        if not alert_ids:
            flash('请选择要创建采购申请的预警', 'warning')
            return redirect(url_for('inventory_alert.index'))

        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        # 获取所有选中的预警
        alerts = InventoryAlert.query.filter(
            InventoryAlert.id.in_(alert_ids),
            InventoryAlert.area_id.in_(area_ids),
            InventoryAlert.status == '未处理'
        ).all()

        # 按区域分组
        alerts_by_area = {}
        for alert in alerts:
            if alert.area_id not in alerts_by_area:
                alerts_by_area[alert.area_id] = []
            alerts_by_area[alert.area_id].append(alert)

        # 获取区域信息
        areas = {}
        for area_id in alerts_by_area.keys():
            area = AdministrativeArea.query.get(area_id)
            areas[area_id] = area

        return render_template('inventory_alert/batch_create_requisition.html',
                              alerts_by_area=alerts_by_area,
                              areas=areas)
    else:
        # 处理POST请求，创建采购申请
        alert_ids = request.form.getlist('alert_ids[]', type=int)
        required_date = request.form.get('required_date')
        notes = request.form.get('notes')

        if not alert_ids or not required_date:
            flash('请填写所有必填字段', 'warning')
            return redirect(url_for('inventory_alert.index'))

        # 获取当前用户可访问的区域
        accessible_areas = current_user.get_accessible_areas()
        area_ids = [area.id for area in accessible_areas]

        # 获取所有选中的预警
        alerts = InventoryAlert.query.filter(
            InventoryAlert.id.in_(alert_ids),
            InventoryAlert.area_id.in_(area_ids),
            InventoryAlert.status == '未处理'
        ).all()

        # 按区域分组
        alerts_by_area = {}
        for alert in alerts:
            if alert.area_id not in alerts_by_area:
                alerts_by_area[alert.area_id] = []
            alerts_by_area[alert.area_id].append(alert)

        # 为每个区域创建一个采购申请
        for area_id, area_alerts in alerts_by_area.items():
            # 生成采购申请编号
            requisition_number = f"CG{datetime.now().strftime('%Y%m%d%H%M%S')}-{area_id}"

            # 创建采购申请
            requisition = PurchaseRequisition(
                requisition_number=requisition_number,
                area_id=area_id,
                requisition_date=date.today(),
                required_date=datetime.strptime(required_date, '%Y-%m-%d').date(),
                status='待审核',
                created_by=current_user.id,
                notes=notes
            )

            db.session.add(requisition)
            db.session.flush()  # 获取ID

            # 创建采购申请明细
            for alert in area_alerts:
                quantity = request.form.get(f'quantity_{alert.id}', type=float)
                if quantity and quantity > 0:
                    requisition_item = PurchaseRequisitionItem(
                        requisition_id=requisition.id,
                        ingredient_id=alert.ingredient_id,
                        quantity=quantity,
                        unit=alert.unit,
                        notes=f'由库存预警自动生成'
                    )

                    db.session.add(requisition_item)

                    # 更新预警状态
                    alert.status = '已处理'
                    alert.processed_by = current_user.id
                    alert.processed_at = datetime.now()
                    alert.notes = f'已创建采购申请：{requisition_number}'

        db.session.commit()
        flash('采购申请创建成功', 'success')
        return redirect(url_for('purchase_requisition.index'))
