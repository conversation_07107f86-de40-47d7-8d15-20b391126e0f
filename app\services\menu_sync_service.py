"""
菜单同步服务

提供菜单数据同步功能，包括从周菜单或日菜单同步到工作日志。
"""

from datetime import timedelta
from flask import current_app
from app import db
from app.models import WeeklyMenu, WeeklyMenuRecipe
from app.models_daily_management import DailyLog
from sqlalchemy import text


class MenuSyncService:
    """菜单同步服务类"""

    @staticmethod
    def get_menu_text_from_recipes(recipes):
        """
        将菜谱列表转换为文本格式

        Args:
            recipes: 菜谱列表

        Returns:
            str: 菜谱文本，如"红烧肉、清蒸鱼、炒青菜"
        """
        if not recipes:
            return None
        return "、".join([recipe.get('name', '未知菜品') for recipe in recipes])

    @staticmethod
    def sync_weekly_menu_to_daily_logs(weekly_menu_id):
        """
        同步周菜单数据到工作日志

        Args:
            weekly_menu_id: 周菜单ID

        Returns:
            dict: 同步结果
        """
        try:
            # 获取周菜单
            weekly_menu = WeeklyMenu.query.get(weekly_menu_id)
            if not weekly_menu:
                return {'success': False, 'message': '周菜单不存在'}

            # 获取周内所有日期
            start_date = weekly_menu.week_start
            end_date = weekly_menu.week_end
            current_date = start_date

            # 记录同步结果
            sync_results = {
                'total_days': 0,
                'updated_logs': 0,
                'created_logs': 0,
                'skipped_logs': 0,
                'details': []
            }

            # 遍历周内每一天
            while current_date <= end_date:
                sync_results['total_days'] += 1

                # 获取当前日期的工作日志
                log = DailyLog.query.filter_by(
                    log_date=current_date,
                    area_id=weekly_menu.area_id
                ).first()

                # 如果日志不存在且周菜单已发布，则创建日志
                if not log and weekly_menu.status == '已发布':
                    try:
                        # 使用原始SQL创建日志
                        sql = text("""
                        INSERT INTO daily_logs
                        (log_date, area_id, created_by, created_at, updated_at)
                        OUTPUT inserted.id
                        VALUES
                        (CONVERT(DATETIME2(1), :log_date, 23), :area_id, :created_by, GETDATE(), GETDATE())
                        """)

                        result = db.session.execute(sql, {
                            'log_date': current_date.strftime('%Y-%m-%d'),
                            'area_id': weekly_menu.area_id,
                            'created_by': weekly_menu.created_by
                        })
                        log_id = result.scalar()

                        log = DailyLog.query.get(log_id)
                        sync_results['created_logs'] += 1

                    except Exception as e:
                        current_app.logger.error(f"创建工作日志失败: {str(e)}")
                        sync_results['details'].append({
                            'date': current_date.strftime('%Y-%m-%d'),
                            'status': 'error',
                            'message': f"创建日志失败: {str(e)}"
                        })
                        current_date += timedelta(days=1)
                        continue

                # 如果日志存在，更新菜单字段
                if log:
                    # 获取当前日期是周几
                    weekday = current_date.weekday()

                    # 更新标志
                    updated = False
                    menu_updates = {}

                    # 更新工作日志的菜单字段
                    for meal_type, field_name in [('早餐', 'breakfast_menu'), ('午餐', 'lunch_menu'), ('晚餐', 'dinner_menu')]:
                        # 查找对应日期和餐次的菜谱
                        weekly_recipes = WeeklyMenuRecipe.query.filter(
                            WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                            WeeklyMenuRecipe.day_of_week == weekday + 1,
                            WeeklyMenuRecipe.meal_type == meal_type
                        ).all()

                        if weekly_recipes:
                            # 将菜谱列表转换为文本
                            recipe_list = [{'name': recipe.recipe_name} for recipe in weekly_recipes]
                            menu_text = MenuSyncService.get_menu_text_from_recipes(recipe_list)

                            if menu_text:
                                menu_updates[field_name] = menu_text
                                updated = True

                    if updated:
                        try:
                            # 使用原始SQL更新菜单字段，避免精度问题
                            update_sql = text(f"""
                            UPDATE daily_logs
                            SET breakfast_menu = '{menu_updates.get('breakfast_menu', log.breakfast_menu or '')}',
                                lunch_menu = '{menu_updates.get('lunch_menu', log.lunch_menu or '')}',
                                dinner_menu = '{menu_updates.get('dinner_menu', log.dinner_menu or '')}',
                                updated_at = GETDATE()
                            WHERE id = {log.id}
                            """)

                            # 执行SQL
                            db.session.execute(update_sql)

                            db.session.commit()
                            sync_results['updated_logs'] += 1
                            current_app.logger.info(f"更新了日期 {current_date} 的工作日志菜单")
                            sync_results['details'].append({
                                'date': current_date.strftime('%Y-%m-%d'),
                                'status': 'updated',
                                'message': "更新了菜单字段"
                            })
                        except Exception as e:
                            db.session.rollback()
                            current_app.logger.error(f"更新工作日志失败: {str(e)}")
                            sync_results['details'].append({
                                'date': current_date.strftime('%Y-%m-%d'),
                                'status': 'error',
                                'message': f"更新失败: {str(e)}"
                            })
                    else:
                        sync_results['skipped_logs'] += 1
                        sync_results['details'].append({
                            'date': current_date.strftime('%Y-%m-%d'),
                            'status': 'skipped',
                            'message': "没有找到菜谱数据"
                        })
                else:
                    sync_results['skipped_logs'] += 1
                    sync_results['details'].append({
                        'date': current_date.strftime('%Y-%m-%d'),
                        'status': 'skipped',
                        'message': "工作日志不存在且周菜单未发布"
                    })

                # 移动到下一天
                current_date += timedelta(days=1)

            return {'success': True, 'results': sync_results}

        except Exception as e:
            current_app.logger.error(f"同步周菜单到工作日志失败: {str(e)}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def sync_menu_plan_to_daily_log(menu_plan_id):
        """
        同步日菜单数据到工作日志 - 日菜单功能已移除

        Args:
            menu_plan_id: 日菜单ID

        Returns:
            dict: 同步结果
        """
        return {'success': False, 'message': '日菜单功能已移除，请使用周菜单同步功能'}
