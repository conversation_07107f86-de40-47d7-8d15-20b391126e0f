from flask import jsonify, request, current_app, url_for
from flask_login import login_required, current_user
from app import db
from app.api.menu_display import menu_display_bp
from app.models import Recipe, AdministrativeArea, WeeklyMenu, WeeklyMenuRecipe
from datetime import datetime, date, timedelta
from app.utils.json_helper import json_response

@menu_display_bp.route('/week', methods=['GET'])
@login_required
def get_week_menu():
    """获取周菜谱数据（仅展示菜品，不关联食材采购）"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数
    area_id = request.args.get('area_id', type=int)
    week_start_str = request.args.get('week_start')

    # 如果没有指定周开始日期，默认为本周一
    if week_start_str:
        try:
            week_start = datetime.strptime(week_start_str, '%Y-%m-%d').date()
        except ValueError:
            # 如果日期格式不正确，使用当前日期
            today = date.today()
            week_start = today - timedelta(days=today.weekday())  # 本周一
    else:
        today = date.today()
        week_start = today - timedelta(days=today.weekday())  # 本周一

    # 计算周结束日期（周日）
    week_end = week_start + timedelta(days=6)

    # 构建查询 - 改为使用周菜单
    query = WeeklyMenu.query.filter(
        WeeklyMenu.week_start <= week_end,
        WeeklyMenu.week_end >= week_start,
        WeeklyMenu.status == '已发布'
    )

    # 应用区域过滤
    if area_id:
        # 检查用户是否有权限查看该区域
        if area_id not in area_ids:
            return jsonify({'success': False, 'message': '您没有权限查看该区域的菜单计划'}), 403
        query = query.filter_by(area_id=area_id)
        area = AdministrativeArea.query.get(area_id)
        area_name = area.name if area else "未知区域"
    else:
        # 默认使用岳雅学校（示例）
        area = AdministrativeArea.query.filter_by(name='岳阳县岳雅学校').first()
        if area:
            area_id = area.id
            area_name = area.name
            query = query.filter_by(area_id=area_id)
        else:
            # 如果找不到岳雅学校，则使用用户可访问的第一个区域
            if area_ids:
                query = query.filter(WeeklyMenu.area_id.in_(area_ids))
                area = AdministrativeArea.query.get(area_ids[0])
                area_name = area.name if area else "未知区域"
            else:
                return jsonify({'success': False, 'message': '没有可访问的区域'}), 404

    # 获取所有符合条件的周菜单
    weekly_menus = query.all()

    # 构建周视图数据结构
    week_data = []
    for i in range(7):
        current_date = week_start + timedelta(days=i)
        weekday = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][i]

        # 查找当天的菜单计划
        day_meals = {
            '早餐': None,
            '午餐': None,
            '晚餐': None
        }

        # 从周菜单中获取当天的菜品
        for weekly_menu in weekly_menus:
            # 计算当前日期是星期几 (1-7, 1表示周一)
            day_of_week = current_date.weekday() + 1

            # 获取当天的菜品
            weekly_recipes = WeeklyMenuRecipe.query.filter(
                WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                WeeklyMenuRecipe.day_of_week == day_of_week
            ).all()

            for weekly_recipe in weekly_recipes:
                meal_type = weekly_recipe.meal_type
                if meal_type in day_meals:
                    if day_meals[meal_type] is None:
                        day_meals[meal_type] = {
                            'id': weekly_menu.id,
                            'status': weekly_menu.status,
                            'recipes': []
                        }

                    recipe = weekly_recipe.recipe
                    if recipe:
                        day_meals[meal_type]['recipes'].append({
                            'id': recipe.id,
                            'name': recipe.name,
                            'image_url': recipe.main_image or url_for('static', filename='images/default_recipe.png'),
                            'description': recipe.description or '',
                            'tags': [recipe.category_rel.name if recipe.category_rel else '']
                        })

        week_data.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'weekday': weekday,
            'meals': day_meals
        })

    return json_response({
        'success': True,
        'week_start': week_start,  # 直接传递datetime对象，json_response会处理
        'week_end': week_end,      # 直接传递datetime对象，json_response会处理
        'area_id': area_id,
        'area_name': area_name,
        'week_data': week_data
    })

@menu_display_bp.route('/recipe/<int:recipe_id>', methods=['GET'])
@login_required
def get_recipe_details(recipe_id):
    """获取菜品详情（不包含食材用量）"""
    recipe = Recipe.query.get_or_404(recipe_id)

    # 构建菜品详情，但不包含食材用量
    recipe_data = {
        'id': recipe.id,
        'name': recipe.name,
        'category': recipe.category_rel.name if recipe.category_rel else '',
        'image_url': recipe.main_image or url_for('static', filename='images/default_recipe.png'),
        'description': recipe.description or '',
        'nutrition': {
            'calories': recipe.calories or 0
        },
        'cooking_method': recipe.cooking_method or '',
        'cooking_time': recipe.cooking_time or 0,
        'cooking_steps': recipe.cooking_steps or '',
        'tags': [recipe.category_rel.name if recipe.category_rel else '']
    }

    return json_response({
        'success': True,
        'recipe': recipe_data
    })

@menu_display_bp.route('/week/print', methods=['GET'])
@login_required
def print_week_menu():
    """获取适合打印的周菜谱数据"""
    # 复用get_week_menu的逻辑，但返回适合打印的格式
    response = get_week_menu()

    # 如果是错误响应，直接返回
    if response.status_code != 200:
        return response

    # 获取JSON数据
    data = response.get_json()

    # 构建打印友好的数据结构
    print_data = {
        'title': f"{data['area_name']}周菜谱 ({data['week_start']} 至 {data['week_end']})",
        'area_name': data['area_name'],
        'week_start': data['week_start'],
        'week_end': data['week_end'],
        'week_data': data['week_data']
    }

    return json_response({
        'success': True,
        'print_data': print_data
    })
