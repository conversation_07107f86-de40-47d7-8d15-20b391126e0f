"""
特殊事件服务

提供食堂特殊事件管理所需的数据服务，包括特殊事件的创建、获取、更新和删除等。
"""

from datetime import datetime
from app import db
from app.models_daily_management import SpecialEvent, Photo
from app.utils.log_activity import log_activity


class EventService:
    """特殊事件服务类"""

    @staticmethod
    def get_events_by_daily_log(daily_log_id):
        """
        获取日志的特殊事件
        
        Args:
            daily_log_id: 日志ID
            
        Returns:
            list: 特殊事件列表
        """
        return SpecialEvent.query.filter_by(daily_log_id=daily_log_id).all()

    @staticmethod
    def get_event_by_id(event_id):
        """
        根据ID获取特殊事件
        
        Args:
            event_id: 特殊事件ID
            
        Returns:
            SpecialEvent: 特殊事件对象
        """
        return SpecialEvent.query.get(event_id)

    @staticmethod
    def create_event(data):
        """
        创建特殊事件
        
        Args:
            data: 特殊事件数据
            
        Returns:
            SpecialEvent: 创建的特殊事件对象
        """
        event = SpecialEvent(
            daily_log_id=data.get('daily_log_id'),
            event_type=data.get('event_type'),
            event_time=data.get('event_time'),
            location=data.get('location'),
            description=data.get('description'),
            participants=data.get('participants'),
            handling_process=data.get('handling_process'),
            result=data.get('result')
        )
        
        db.session.add(event)
        db.session.commit()
        
        # 记录活动
        log_activity('创建了特殊事件', f'类型: {event.event_type}', 'event', event.id)
        
        return event

    @staticmethod
    def update_event(event_id, data):
        """
        更新特殊事件
        
        Args:
            event_id: 特殊事件ID
            data: 特殊事件数据
            
        Returns:
            SpecialEvent: 更新后的特殊事件对象
        """
        event = SpecialEvent.query.get(event_id)
        
        if not event:
            return None
            
        # 更新特殊事件
        event.event_type = data.get('event_type', event.event_type)
        event.event_time = data.get('event_time', event.event_time)
        event.location = data.get('location', event.location)
        event.description = data.get('description', event.description)
        event.participants = data.get('participants', event.participants)
        event.handling_process = data.get('handling_process', event.handling_process)
        event.result = data.get('result', event.result)
        
        db.session.commit()
        
        # 记录活动
        log_activity('更新了特殊事件', f'类型: {event.event_type}', 'event', event.id)
        
        return event

    @staticmethod
    def delete_event(event_id):
        """
        删除特殊事件
        
        Args:
            event_id: 特殊事件ID
            
        Returns:
            bool: 是否删除成功
        """
        event = SpecialEvent.query.get(event_id)
        
        if not event:
            return False
            
        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='event', reference_id=event_id).all()
        for photo in photos:
            db.session.delete(photo)
            
        # 记录活动
        event_type = event.event_type
        
        # 删除特殊事件
        db.session.delete(event)
        db.session.commit()
        
        # 记录活动
        log_activity('删除了特殊事件', f'类型: {event_type}', 'event', event_id)
        
        return True
