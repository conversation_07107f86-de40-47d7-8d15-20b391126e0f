#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多场景适配引导服务
根据不同类型学校提供定制化引导内容
"""

from flask import current_app
from datetime import datetime
import json

class ScenarioGuideService:
    """多场景适配引导服务类"""
    
    # 学校类型定义
    SCHOOL_TYPES = {
        'primary': {
            'name': '小学',
            'characteristics': ['学生年龄小', '营养需求特殊', '食品安全要求高', '家长关注度高'],
            'focus_areas': ['营养搭配', '食品安全', '陪餐管理', '家长沟通']
        },
        'middle': {
            'name': '中学',
            'characteristics': ['学生食量大', '营养需求多样', '用餐时间集中', '成本控制重要'],
            'focus_areas': ['成本控制', '效率管理', '营养均衡', '食材采购']
        },
        'high': {
            'name': '高中',
            'characteristics': ['学习压力大', '营养需求高', '用餐时间紧张', '多样化需求'],
            'focus_areas': ['快速供餐', '营养补充', '多样化菜品', '时间管理']
        },
        'vocational': {
            'name': '职业学校',
            'characteristics': ['学生年龄较大', '实用性要求高', '成本敏感', '管理相对灵活'],
            'focus_areas': ['成本效益', '实用管理', '灵活配置', '效率优化']
        },
        'university': {
            'name': '大学',
            'characteristics': ['规模大', '多样化需求', '管理复杂', '服务标准高'],
            'focus_areas': ['规模化管理', '多元化服务', '质量控制', '数据分析']
        },
        'rural': {
            'name': '乡村学校',
            'characteristics': ['资源有限', '人员较少', '简化管理', '基础保障'],
            'focus_areas': ['简化流程', '基础功能', '人员培训', '成本控制']
        },
        'urban': {
            'name': '城市学校',
            'characteristics': ['设施完善', '要求较高', '管理规范', '技术应用'],
            'focus_areas': ['精细化管理', '技术应用', '标准化流程', '质量提升']
        }
    }
    
    # 场景化引导内容
    SCENARIO_GUIDES = {
        'primary': {
            'welcome_message': '欢迎使用专为小学设计的食堂管理系统！我们将重点关注营养搭配和食品安全。',
            'priority_steps': ['daily_management', 'food_samples', 'suppliers', 'weekly_menu'],
            'customized_content': {
                'daily_management': {
                    'emphasis': '小学生食品安全检查特别重要，建议每日多次检查并详细记录。',
                    'tips': [
                        '重点检查食材新鲜度',
                        '关注过敏原标识',
                        '确保餐具清洁卫生',
                        '记录学生用餐反应'
                    ]
                },
                'weekly_menu': {
                    'emphasis': '小学生营养需求特殊，菜单制定需要特别关注营养均衡。',
                    'tips': [
                        '确保蛋白质充足',
                        '增加蔬菜水果比例',
                        '控制油盐糖用量',
                        '考虑季节性食材'
                    ]
                }
            }
        },
        'middle': {
            'welcome_message': '欢迎使用中学食堂管理系统！我们将重点关注成本控制和效率管理。',
            'priority_steps': ['purchase_order', 'stock_in', 'consumption_plan', 'weekly_menu'],
            'customized_content': {
                'purchase_order': {
                    'emphasis': '中学生食量大，采购量计算需要精确，避免浪费。',
                    'tips': [
                        '根据实际用餐人数计算',
                        '考虑季节性价格波动',
                        '建立多个供应商备选',
                        '定期评估采购成本'
                    ]
                },
                'consumption_plan': {
                    'emphasis': '合理的消耗计划能有效控制成本，减少浪费。',
                    'tips': [
                        '分析历史消耗数据',
                        '考虑学生用餐习惯',
                        '预留适当安全库存',
                        '定期调整计划参数'
                    ]
                }
            }
        },
        'rural': {
            'welcome_message': '欢迎使用适合乡村学校的简化食堂管理系统！我们将重点关注简化流程和基础功能。',
            'priority_steps': ['suppliers', 'weekly_menu', 'daily_management', 'stock_in'],
            'simplified_mode': True,
            'customized_content': {
                'suppliers': {
                    'emphasis': '建议选择本地可靠的供应商，确保食材新鲜和供应稳定。',
                    'tips': [
                        '优先选择本地供应商',
                        '建立长期合作关系',
                        '关注供应商信誉',
                        '定期评估供应质量'
                    ]
                },
                'daily_management': {
                    'emphasis': '虽然人员有限，但基础的安全检查不能省略。',
                    'tips': [
                        '制定简化的检查清单',
                        '重点关注关键环节',
                        '培训多技能人员',
                        '建立应急处理预案'
                    ]
                }
            }
        },
        'university': {
            'welcome_message': '欢迎使用大学食堂管理系统！我们将重点关注规模化管理和数据分析。',
            'priority_steps': ['traceability', 'purchase_order', 'stock_in', 'consumption_plan'],
            'advanced_features': True,
            'customized_content': {
                'traceability': {
                    'emphasis': '大学食堂规模大，完善的溯源系统是食品安全的重要保障。',
                    'tips': [
                        '建立完整的追溯档案',
                        '定期进行溯源演练',
                        '与供应商建立信息共享',
                        '利用数据分析优化管理'
                    ]
                },
                'purchase_order': {
                    'emphasis': '大规模采购需要精细化管理和成本优化。',
                    'tips': [
                        '实施集中采购策略',
                        '建立供应商评价体系',
                        '利用数据分析优化采购',
                        '建立风险管控机制'
                    ]
                }
            }
        }
    }
    
    @staticmethod
    def detect_school_type(school_name, area_info=None):
        """根据学校名称和区域信息自动检测学校类型"""
        school_name_lower = school_name.lower()
        
        # 根据学校名称关键词判断
        if any(keyword in school_name_lower for keyword in ['小学', 'primary', '实验小学']):
            return 'primary'
        elif any(keyword in school_name_lower for keyword in ['中学', 'middle', '初中']):
            return 'middle'
        elif any(keyword in school_name_lower for keyword in ['高中', 'high', '高级中学']):
            return 'high'
        elif any(keyword in school_name_lower for keyword in ['职业', 'vocational', '技校', '职校']):
            return 'vocational'
        elif any(keyword in school_name_lower for keyword in ['大学', 'university', '学院', 'college']):
            return 'university'
        
        # 根据区域信息判断城乡类型
        if area_info and area_info.get('is_township_school'):
            return 'rural'
        else:
            return 'urban'
    
    @staticmethod
    def get_scenario_guide(school_type):
        """获取指定学校类型的场景化引导内容"""
        return ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
    
    @staticmethod
    def get_customized_step_content(school_type, step_name):
        """获取定制化的步骤内容"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        customized = scenario.get('customized_content', {})
        return customized.get(step_name, {})
    
    @staticmethod
    def get_priority_steps(school_type):
        """获取指定学校类型的优先步骤"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        return scenario.get('priority_steps', [])
    
    @staticmethod
    def is_simplified_mode(school_type):
        """判断是否使用简化模式"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        return scenario.get('simplified_mode', False)
    
    @staticmethod
    def has_advanced_features(school_type):
        """判断是否启用高级功能"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        return scenario.get('advanced_features', False)
    
    @staticmethod
    def get_welcome_message(school_type):
        """获取定制化的欢迎消息"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        return scenario.get('welcome_message', '欢迎使用校园餐智慧食堂平台！')
    
    @staticmethod
    def generate_scenario_report(school_type):
        """生成场景分析报告"""
        school_info = ScenarioGuideService.SCHOOL_TYPES.get(school_type, {})
        scenario_guide = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        
        return {
            'school_type': school_type,
            'school_name': school_info.get('name', '未知类型'),
            'characteristics': school_info.get('characteristics', []),
            'focus_areas': school_info.get('focus_areas', []),
            'priority_steps': scenario_guide.get('priority_steps', []),
            'simplified_mode': scenario_guide.get('simplified_mode', False),
            'advanced_features': scenario_guide.get('advanced_features', False),
            'customization_count': len(scenario_guide.get('customized_content', {}))
        }
    
    @staticmethod
    def create_personalized_guide_plan(school_type, user_preferences=None):
        """创建个性化的引导计划"""
        scenario = ScenarioGuideService.SCENARIO_GUIDES.get(school_type, {})
        priority_steps = scenario.get('priority_steps', [])
        
        # 基础引导计划
        guide_plan = {
            'school_type': school_type,
            'welcome_message': scenario.get('welcome_message', ''),
            'estimated_time': '15-25分钟',
            'steps': []
        }
        
        # 根据学校类型调整步骤顺序和内容
        all_steps = [
            'welcome', 'daily_management', 'suppliers', 'ingredients_recipes',
            'weekly_menu', 'purchase_order', 'stock_in', 'consumption_plan',
            'stock_out', 'traceability', 'food_samples', 'completed'
        ]
        
        # 优先步骤放在前面
        ordered_steps = ['welcome']
        for step in priority_steps:
            if step not in ordered_steps:
                ordered_steps.append(step)
        
        # 添加其他步骤
        for step in all_steps[1:-1]:  # 排除welcome和completed
            if step not in ordered_steps:
                ordered_steps.append(step)
        
        ordered_steps.append('completed')
        
        # 生成步骤详情
        for i, step in enumerate(ordered_steps):
            step_info = {
                'step_name': step,
                'order': i,
                'is_priority': step in priority_steps,
                'customized_content': ScenarioGuideService.get_customized_step_content(school_type, step)
            }
            guide_plan['steps'].append(step_info)
        
        return guide_plan
