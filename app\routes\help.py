"""
帮助中心路由
"""
from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from app.utils.school_required import school_required

help_bp = Blueprint('help', __name__, url_prefix='/help')

@help_bp.route('/')
@login_required
@school_required
def index(user_area):
    """帮助中心首页"""
    return render_template('help/index.html', 
                         title='帮助中心',
                         user_area=user_area)

@help_bp.route('/financial')
@login_required
@school_required
def financial_help(user_area):
    """财务管理帮助"""
    return render_template('help/financial.html', 
                         title='财务管理帮助',
                         user_area=user_area)

@help_bp.route('/daily')
@login_required
@school_required
def daily_help(user_area):
    """日常管理帮助"""
    return render_template('help/daily.html', 
                         title='日常管理帮助',
                         user_area=user_area)

@help_bp.route('/supply')
@login_required
@school_required
def supply_help(user_area):
    """供应链管理帮助"""
    return render_template('help/supply.html', 
                         title='供应链管理帮助',
                         user_area=user_area)

@help_bp.route('/system')
@login_required
@school_required
def system_help(user_area):
    """系统设置帮助"""
    return render_template('help/system.html', 
                         title='系统设置帮助',
                         user_area=user_area)

@help_bp.route('/troubleshooting')
@login_required
@school_required
def troubleshooting(user_area):
    """故障排除"""
    return render_template('help/troubleshooting.html', 
                         title='故障排除',
                         user_area=user_area)

@help_bp.route('/accounting-subjects')
@login_required
@school_required
def accounting_subjects_help(user_area):
    """会计科目管理帮助"""
    return render_template('help/accounting_subjects.html', 
                         title='会计科目管理帮助',
                         user_area=user_area)

@help_bp.route('/search')
@login_required
@school_required
def search_help(user_area):
    """搜索帮助内容"""
    query = request.args.get('q', '').strip()
    
    # 这里可以实现搜索逻辑
    # 暂时返回空结果
    results = []
    
    if request.headers.get('Content-Type') == 'application/json':
        return jsonify({'results': results, 'query': query})
    
    return render_template('help/search.html', 
                         title='搜索帮助',
                         query=query,
                         results=results,
                         user_area=user_area)
